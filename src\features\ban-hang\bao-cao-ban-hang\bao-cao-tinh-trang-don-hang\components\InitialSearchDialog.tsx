import React from 'react';

import { Button } from '@mui/material';
import { searchSchema, initialSearchValues, SearchFormValues, transformSearchValuesForAPI } from '../schema';
import { AritoDialog, AritoForm, AritoHeaderTabs } from '@/components/custom/arito';
import { useSearchFieldStates } from '../hooks/useSearchFieldStates';
import { DetailsTab, BasicInfo, OtherTab } from './tabs';
import { AritoIcon } from '@/components/custom/arito';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const searchFieldStates = useSearchFieldStates();

  const handleFormSubmit = async (data: any) => {
    const searchFieldData = searchFieldStates.getSearchFieldData();
    const combinedData = {
      ...data,
      ...searchFieldData
    };

    // Transform UI values to API values (convert __all__ to empty string)
    const transformedData = transformSearchValuesForAPI(combinedData);

    const requestBody = {
      ngay_ct1: transformedData.ngay_ct1 || '',
      ngay_ct2: transformedData.ngay_ct2 || '',
      so_ct1: transformedData.so_ct1 || '',
      so_ct2: transformedData.so_ct2 || '',
      ma_kh: combinedData.ma_kh || '',
      nh_kh1: combinedData.nh_kh1 || '',
      nh_kh2: combinedData.nh_kh2 || '',
      nh_kh3: combinedData.nh_kh3 || '',
      ma_vt: combinedData.ma_vt || '',
      ma_lvt: combinedData.ma_lvt || '',
      nh_vt1: combinedData.nh_vt1 || '',
      nh_vt2: combinedData.nh_vt2 || '',
      nh_vt3: combinedData.nh_vt3 || '',
      ma_kho: combinedData.ma_kho || '',
      ma_gd: combinedData.ma_gd || '',
      ma_lo: combinedData.ma_lo || '',
      ma_vi_tri: combinedData.ma_vi_tri || '',
      ngay_giao: transformedData.ngay_giao || '',
      status: transformedData.status || '',
      report_filtering: transformedData.report_filtering || '',
      dien_giai: transformedData.dien_giai || '',
      ma_ngv: transformedData.ma_ngv || '',
      mau_bc:
        typeof transformedData.mau_bc === 'number' ? transformedData.mau_bc : parseInt(transformedData.mau_bc || '20'),
      ma_unit: transformedData.ma_unit || ''
    };

    try {
      await onSearch(requestBody);
      onClose();
    } catch (error) {
      console.error('Error submitting search:', error);
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Báo cáo tình trạng đơn hàng'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialSearchValues}
        onSubmit={handleFormSubmit}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
            <BasicInfo />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailsTab searchFieldStates={searchFieldStates} />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab searchFieldStates={searchFieldStates} />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 px-2'
        bottomBar={
          <>
            <Button
              className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
              type='submit'
              variant='contained'
            >
              <AritoIcon icon={884} marginX='4px' />
              Đồng ý
            </Button>

            <Button onClick={onClose} variant='outlined'>
              <AritoIcon icon={885} marginX='4px' />
              Huỷ
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
