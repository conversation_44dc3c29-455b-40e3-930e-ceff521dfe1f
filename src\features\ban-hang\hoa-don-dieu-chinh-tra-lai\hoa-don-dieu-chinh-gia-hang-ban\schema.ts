import { z } from 'zod';

export const searchSchema = z.object({
  ngay_ct1: z.string().optional().nullable(),
  ngay_ct2: z.string().optional().nullable(),
  so_ct1: z.string().optional().nullable(),
  so_ct2: z.string().optional().nullable(),
  dien_giai: z.string().optional().nullable(),
  status: z.string().optional().nullable(),
  user_id0: z.string().optional().nullable()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const formSchema = z.object({
  ma_ngv: z.string().optional().nullable(),
  ma_so_thue: z.string().optional().nullable(),
  ten_kh_thue: z.string().optional().nullable(),
  dia_chi: z.string().optional().nullable(),
  ong_ba: z.string().optional().nullable(),
  e_mail: z.string().optional().nullable(),
  dien_giai: z.string().optional().nullable(),
  ngay_ct: z.string().optional().nullable(),
  status: z.string().optional().nullable(),
  transfer_yn: z.boolean().optional().nullable(),
  ma_tthddt: z.string().optional().nullable(),
  ma_pttt: z.string().optional().nullable(),
  so_ct_hddt: z.string().optional().nullable(),
  ngay_ct_hddt: z.string().optional().nullable(),
  so_ct2_hddt: z.string().optional().nullable(),
  ma_mau_ct_hddt: z.string().optional().nullable(),
  ten_vt_thue: z.string().optional().nullable(),
  ly_do_huy: z.string().optional().nullable(),
  ly_do: z.string().optional().nullable(),
  ghi_chu: z.string().optional().nullable()
});

export type FormSchema = z.infer<typeof formSchema>;

export const initialFormValues: FormSchema = {
  ma_ngv: '1',
  ma_so_thue: '',
  ten_kh_thue: '',
  dia_chi: '',
  ong_ba: '',
  e_mail: '',
  dien_giai: '',
  ngay_ct: new Date().toISOString().split('T')[0],
  status: '5',
  transfer_yn: true,
  ma_tthddt: '0',
  ma_pttt: null,
  so_ct_hddt: '',
  ngay_ct_hddt: '',
  so_ct2_hddt: '',
  ma_mau_ct_hddt: '',
  ten_vt_thue: '',
  ly_do_huy: '',
  ly_do: '',
  ghi_chu: ''
};
