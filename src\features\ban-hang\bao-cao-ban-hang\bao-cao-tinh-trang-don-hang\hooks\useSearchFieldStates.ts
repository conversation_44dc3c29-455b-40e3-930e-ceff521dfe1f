import { useState, useCallback } from 'react';

/**
 * Hook for managing search field states in the bao-cao-tinh-trang-don-hang-mua feature
 *
 * This hook manages all SearchField states that are not handled by AritoForm directly.
 * SearchField components use onRowSelection to update these states.
 */
export function useSearchFieldStates() {
  const [customer, setCustomer] = useState<any>(null);
  const [customerGroup1, setCustomerGroup1] = useState<any>(null);
  const [customerGroup2, setCustomerGroup2] = useState<any>(null);
  const [customerGroup3, setCustomerGroup3] = useState<any>(null);

  const [material, setMaterial] = useState<any>(null);
  const [materialType, setMaterialType] = useState<any>(null);
  const [materialGroup1, setMaterialGroup1] = useState<any>(null);
  const [materialGroup2, setMaterialGroup2] = useState<any>(null);
  const [materialGroup3, setMaterialGroup3] = useState<any>(null);

  const [warehouse, setWarehouse] = useState<any>(null);
  const [lot, setLot] = useState<any>(null);
  const [location, setLocation] = useState<any>(null);

  const [transaction, setTransaction] = useState<any>(null);

  const resetSearchFields = useCallback(() => {
    setCustomer(null);
    setCustomerGroup1(null);
    setCustomerGroup2(null);
    setCustomerGroup3(null);
    setMaterial(null);
    setMaterialType(null);
    setMaterialGroup1(null);
    setMaterialGroup2(null);
    setMaterialGroup3(null);
    setWarehouse(null);
    setLot(null);
    setLocation(null);
    setTransaction(null);
  }, []);

  const getSearchFieldData = useCallback(() => {
    return {
      ma_kh: customer?.uuid || '',
      nh_kh1: customerGroup1?.uuid || '',
      nh_kh2: customerGroup2?.uuid || '',
      nh_kh3: customerGroup3?.uuid || '',

      ma_vt: material?.uuid || '',
      ma_lvt: materialType?.uuid || '',
      nh_vt1: materialGroup1?.uuid || '',
      nh_vt2: materialGroup2?.uuid || '',
      nh_vt3: materialGroup3?.uuid || '',

      ma_kho: warehouse?.uuid || '',
      ma_lo: lot?.uuid || '',
      ma_vi_tri: location?.uuid || '',

      ma_gd: transaction?.uuid || ''
    };
  }, [
    customer,
    customerGroup1,
    customerGroup2,
    customerGroup3,
    material,
    materialType,
    materialGroup1,
    materialGroup2,
    materialGroup3,
    warehouse,
    lot,
    location,
    transaction
  ]);

  return {
    customer,
    setCustomer,
    customerGroup1,
    setCustomerGroup1,
    customerGroup2,
    setCustomerGroup2,
    customerGroup3,
    setCustomerGroup3,
    material,
    setMaterial,
    materialType,
    setMaterialType,
    materialGroup1,
    setMaterialGroup1,
    materialGroup2,
    setMaterialGroup2,
    materialGroup3,
    setMaterialGroup3,
    warehouse,
    setWarehouse,
    lot,
    setLot,
    location,
    setLocation,
    transaction,
    setTransaction,

    resetSearchFields,
    getSearchFieldData
  };
}
