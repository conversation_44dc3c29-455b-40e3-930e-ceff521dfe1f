import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

// Helper function to render cells with bold formatting for summary rows
const renderCellWithBold = (params: GridRenderCellParams) => {
  const isSummaryRow = params.row.systotal === 'TOTAL' || params.row.id === 'SUMMARY_ROW';
  return <span className={isSummaryRow ? 'font-bold text-blue-800' : ''}>{params.value}</span>;
};

// Helper function to convert status string to status name
const getStatusName = (statusValue: string): string => {
  switch (statusValue) {
    case '0':
      return 'Lập chứng từ';
    case '1':
      return 'Chờ duyệt';
    case '3':
      return 'Chờ xuất';
    case '4':
      return 'Đang duyệt';
    case '5':
      return 'Đã duyệt';
    case '6':
      return 'Đang thực hiện';
    case '7':
      return 'Đã hoàn thành';
    case '9':
      return 'Đóng';
    default:
      return `Trạng thái ${statusValue}`;
  }
};

export const orderStatusReportColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  {
    field: 'unit_id',
    headerName: 'Đơn vị',
    width: 90
  },
  {
    field: 'ma_ct',
    headerName: 'Loại chứng từ',
    width: 120
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày c/từ',
    width: 100
  },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 140
  },
  {
    field: 'ma_kh',
    headerName: 'Mã nhà cung cấp',
    width: 120
  },
  {
    field: 'ten_kh',
    headerName: 'Tên nhà cung cấp',
    width: 250,
    renderCell: renderCellWithBold
  },
  {
    field: 'ma_nvbh',
    headerName: 'Mã nhân viên bán hàng',
    width: 150
  },
  {
    field: 'ten_nvbh',
    headerName: 'Tên nhân viên bán hàng',
    width: 200
  },
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 200,
    renderCell: (params: GridRenderCellParams) => {
      const isSummaryRow = params.row.systotal === 'TOTAL' || params.row.id === 'SUMMARY_ROW';
      const statusText = isSummaryRow ? '' : getStatusName(params.value);
      return <span className={isSummaryRow ? 'font-bold text-blue-800' : ''}>{statusText}</span>;
    }
  },
  {
    field: 'ma_vt',
    headerName: 'Mã vật tư',
    width: 120
  },
  {
    field: 'ten_vt',
    headerName: 'Tên vật tư',
    width: 200,
    renderCell: renderCellWithBold
  },
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 80
  },
  {
    field: 'he_so',
    headerName: 'Hệ số',
    width: 80,
    renderCell: (params: GridRenderCellParams) => {
      const isSummaryRow = params.row.systotal === 'TOTAL' || params.row.id === 'SUMMARY_ROW';
      // Hiển thị trống cho summary row, ngược lại hiển thị giá trị
      const displayValue = isSummaryRow ? '' : params.value;
      return <span className={isSummaryRow ? 'font-bold text-blue-800' : ''}>{displayValue}</span>;
    }
  },
  {
    field: 'gia2',
    headerName: 'Đơn giá',
    width: 100,
    renderCell: renderCellWithBold
  },
  {
    field: 'tien2',
    headerName: 'Thành tiền',
    width: 100,
    renderCell: renderCellWithBold
  },
  {
    field: 'so_luong',
    headerName: 'Sl đặt hàng',
    width: 100,
    renderCell: renderCellWithBold
  },
  {
    field: 'sl_dh',
    headerName: 'Sl đặt đơn hàng',
    width: 100,
    renderCell: renderCellWithBold
  },
  {
    field: 'sl_xuat',
    headerName: 'Sl đã xuất',
    width: 100,
    renderCell: renderCellWithBold
  },
  {
    field: 'sl_hd',
    headerName: 'Sl hóa đơn',
    width: 100,
    renderCell: renderCellWithBold
  },
  {
    field: 'sl_tl',
    headerName: 'Sl trả lại',
    width: 100,
    renderCell: renderCellWithBold
  },
  {
    field: 'sl_cl',
    headerName: 'Sl còn lại',
    width: 100,
    renderCell: renderCellWithBold
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 130
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 100
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 150
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất sản xuất',
    width: 130
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 100
  }
];
