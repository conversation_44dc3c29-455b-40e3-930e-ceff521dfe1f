import { <PERSON>Field, FormField } from '@/components/custom/arito';
import { accountSearchColumns, QUERY_KEYS } from '@/constants';
import { Label } from '@/components/ui/label';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const BasicInfoTab = ({ formMode }: Props) => {
  return (
    <div className='space-y-4 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              dialogTitle='Danh mục tài khoản'
              columnDisplay='code'
              displayRelatedField='name'
              disabled={formMode === 'view'}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ngày</Label>
          <div className='w-[57.5%]'>
            <FormField name='ngay_ct' type='date' disabled={formMode === 'view'} className='w-full' />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
