'use client';

import { useState } from 'react';
import Split from 'react-split';
import { AritoDataTables, LoadingOverlay, InputTable, DeleteDialog } from '@/components/custom/arito';
import { SearchDialog, ActionBar, FormDialog, InputTableAction } from './components';
import { getDataTableColumns, getInputTableColumns } from './cols-definition';
import { useCRUD, useFormState, useRows } from '@/hooks';
import { QUERY_KEYS } from '@/constants';

export default function PhieuChiPage() {
  const [showSearchDialog, setShowSearchDialog] = useState(false);
  const { addItem, updateItem, deleteItem, refreshData, isLoading, data, totalItems, currentPage, handlePageChange } =
    useCRUD<any, any>({
      endpoint: QUERY_KEYS.PHIEU_CHI
    });
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();
  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();
  const handleSearch = () => {
    setShowSearchDialog(true);
  };

  const handleSearchClose = () => {
    setShowSearchDialog(false);
  };

  const handleSearchSubmit = (filters: any) => {};

  const handleSubmit = async (data: any) => {
    try {
      if (formMode === 'add') {
        await addItem(data);
      } else if (formMode === 'edit' && selectedObj) {
        await updateItem(selectedObj.uuid, data);
      }
      handleCloseForm();
      clearSelection();
      refreshData();
    } catch (error) {
      return;
    }
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: data,
      columns: getDataTableColumns(handleViewClick)
    },
    {
      name: 'Lập chứng từ',
      rows: data.filter(row => row.status === '0'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#EAD1DC]' />
    },
    {
      name: 'Chờ duyệt',
      rows: data.filter(row => row.status === '3'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-red-500' />
    },
    {
      name: 'Xóa hóa đơn',
      rows: data.filter(row => row.status === '5'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#3D85C6]' />
    },
    {
      name: 'Khác',
      rows: data.filter(row => row.status === '99'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-black' />
    }
  ];

  return (
    <div className='flex h-full min-h-[calc(100vh-120px)] w-full flex-col overflow-hidden'>
      {showSearchDialog && (
        <SearchDialog open={showSearchDialog} onClose={handleSearchClose} onSearch={handleSearchSubmit} />
      )}

      {showForm && (
        <FormDialog
          formMode={formMode}
          open={showForm}
          onClose={handleCloseForm}
          initialData={formMode === 'add' && !isCopyMode ? undefined : selectedObj}
          onSubmit={handleSubmit}
          onAdd={handleAddClick}
          onEdit={handleEditClick}
          onDelete={handleDeleteClick}
          onCopy={handleCopyClick}
        />
      )}

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteObj={deleteItem}
          clearSelection={clearSelection}
        />
      )}

      {!showForm && (
        <>
          <ActionBar
            onAdd={handleAddClick}
            onEdit={handleEditClick}
            onDelete={handleDeleteClick}
            onCopy={handleCopyClick}
            onSearch={handleSearch}
            onRefresh={async () => {
              await refreshData();
            }}
            isEditDisabled={!selectedObj}
          />

          {isLoading && (
            <div className='flex h-full items-center justify-center'>
              <LoadingOverlay />
            </div>
          )}

          {!isLoading && (
            <Split
              className='flex flex-1 flex-col overflow-hidden'
              direction='vertical'
              sizes={[50, 50]}
              minSize={200}
              gutterSize={4}
              gutterAlign='center'
              snapOffset={30}
              dragInterval={1}
              cursor='row-resize'
            >
              <div className='w-full overflow-hidden'>
                <AritoDataTables
                  tables={tables}
                  onRowClick={handleRowClick}
                  totalItems={totalItems}
                  currentPage={currentPage}
                  onPageChange={handlePageChange}
                  serverSidePagination={true}
                  selectedRowId={selectedRowIndex || undefined}
                />
              </div>

              <div className='max-h-[300px] overflow-hidden'>
                <InputTable
                  rows={selectedObj?.chi_tiet || []}
                  columns={getInputTableColumns()}
                  mode={formMode}
                  actionButtons={<InputTableAction formMode={formMode} />}
                />
              </div>
            </Split>
          )}
        </>
      )}
    </div>
  );
}
