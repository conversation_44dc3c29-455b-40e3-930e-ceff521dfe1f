import { GridRenderCellParams, GridColDef } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  boPhanSearchColumns,
  dotThanhToanSearchColumns,
  hopDongSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  vuViecSearchColumns,
  accountSearchColumns,
  khachHangSearchColumns,
  chiPhiKhongHopLeSearchColumns
} from '@/constants';
import {
  BoPhan,
  ChiPhiKhongHopLeData,
  DotThanhToan,
  HopDong,
  KhachHang,
  KheUoc,
  Phi,
  TaiKhoan,
  VatTu,
  VuViec
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';
import { FormMode } from '@/types/form';

export const getDetailTableColumns = (
  formMode: FormMode,
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'tk',
    headerName: 'Tài khoản',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<TaiKhoan>
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.tk_data?.code || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_data', row)}
      />
    )
  },
  {
    field: 'ten_tk',
    headerName: 'Tên tài khoản',
    width: 200,
    renderCell: (params: GridRenderCellParams) => (
      <CellField name='ten_tk' type='text' value={params.row.tk_data?.name || ''} />
    )
  },
  {
    field: 'ma_kh',
    headerName: 'Mã đối tượng',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<KhachHang>
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục đối tượng'
        value={params.row.ma_kh_data?.customer_code || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh_data', row)}
      />
    )
  },
  {
    field: 'ten_kh',
    headerName: 'Tên đối tượng',
    width: 200,
    renderCell: (params: GridRenderCellParams) => (
      <CellField name='ten_kh' type='text' value={params.row.ma_kh_data?.customer_name || ''} />
    )
  },
  {
    field: 'ps_no_nt',
    headerName: 'Ps nợ VND',
    width: 120,
    type: 'number',
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='ps_no_nt'
        type='number'
        value={params.row.ps_no_nt}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ps_no_nt', newValue)}
      />
    )
  },
  {
    field: 'ps_co_nt',
    headerName: 'Ps có VND',
    width: 120,
    type: 'number',
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='ps_co_nt'
        type='number'
        value={params.row.ps_co_nt}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ps_co_nt', newValue)}
      />
    )
  },
  {
    field: 'nh_dk',
    headerName: 'Nhóm',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='nh_dk'
        type='number'
        value={params.row.nh_dk || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'nh_dk', newValue)}
      />
    )
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 250,
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='dien_giai'
        type='text'
        value={params.row.dien_giai || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'dien_giai', newValue)}
      />
    )
  },
  {
    field: 'so_ct0',
    headerName: 'Số hóa đơn',
    width: 150,
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='so_ct0'
        type='text'
        value={params.row.so_ct0 || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'so_ct0', newValue)}
      />
    )
  },
  {
    field: 'ngay_ct0',
    headerName: 'Ngày hóa đơn',
    width: 150,
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='ngay_ct0'
        type='date'
        value={params.row.ngay_ct0 || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ngay_ct0', newValue)}
      />
    )
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 150,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<BoPhan>
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        columnDisplay='ma_bp'
        dialogTitle='Danh mục bộ phận'
        value={params.row.ma_bp_data?.ma_bp || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_bp_data', row)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 150,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<VuViec>
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vu_viec'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vu_viec || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 150,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<HopDong>
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 150,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<DotThanhToan>
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 150,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<KheUoc>
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<Phi>
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 150,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<VatTu>
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục sản phẩm'
        value={params.row.ma_sp_data?.ma_vt || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_sp_data', row)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 150,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<any>
        searchEndpoint={'/'}
        searchColumns={lenhSanXuatSearchColumns}
        columnDisplay='so_lsx'
        dialogTitle='Danh mục lệnh sản xuất'
        value={params.row.ma_lsx_data?.so_lsx || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 150,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<ChiPhiKhongHopLeData>
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
        searchColumns={chiPhiKhongHopLeSearchColumns}
        columnDisplay='ma_cp_khl'
        dialogTitle='Danh mục chi phí không hợp lệ'
        value={params.row.ma_cp0_data?.ma_cpkhl || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_cp0_data', row)}
      />
    )
  }
];
