import { format } from 'date-fns';
import { transformDocumentNumber } from '@/components/custom/arito/form/document-number/util';
import { MA_CHUNG_TU } from '@/constants';
import { FormFieldState } from '../hooks';

/**
 * Transform detail rows for PhieuThanhToanTamUng API submission
 * @param detailRows - Array of detail row data from the form
 * @returns Transformed detail rows ready for API submission
 */
export const transformDetailRows = (detailRows: any[]) => {
  return detailRows.map((row: any, index: number) => ({
    line: index + 1,
    tk_no: row.tk_no_data?.uuid || row.tk_no || '',
    ma_kh: row.ma_kh_data?.uuid || row.ma_kh || '',
    tien_nt: parseFloat(row.tien_nt) || 0,
    tien: parseFloat(row.tien_nt) || 0,
    dien_giai: row.dien_giai || '',
    ma_loai_hd: row.ma_loai_hd || '0',
    ma_thue: row.ma_thue_data?.uuid || null,
    ten_thue: row.ma_thue_data?.ten_thue || null,
    thue_suat: parseFloat(row.ma_thue_data?.thue_suat) || 0,
    tk_thue: row.tk_thue_data?.uuid || '',
    ten_tk_thue: row.tk_thue_data?.name || '',
    thue_nt: parseFloat(row.thue_nt) || 0,
    thue: parseFloat(row.thue_nt) || 0,
    so_ct0: row.so_ct0 || '',
    so_ct2: row.so_ct2 || '',
    ngay_ct0: row.ngay_ct0 || null,
    ma_mau_ct: row.ma_mau_ct_data?.ma_mau_so || row.ma_mau_ct || '',
    ma_mau_bc: row.ma_mau_bc || '',
    ma_tc_thue: row.ma_tc_thue_data?.uuid || row.ma_tc_thue || '',
    ma_kh_thue: row.ma_kh_thue_data?.uuid || '',
    ten_kh_thue: row.ma_kh_thue_data?.customer_name || row.ten_kh_thue || '',
    dia_chi: row.ma_kh_thue_data?.address || row.dia_chi || '',
    ma_so_thue: row.ma_kh_thue_data?.tax_code || row.ma_so_thue || '',
    ten_vt_thue: row.ma_thue_data?.ten_thue || row.ten_vt_thue || '',
    ma_kh9: row.ma_kh9_data?.uuid || '',
    ten_kh9: row.ma_kh9_data?.customer_name || '',
    ma_bp: row.ma_bp_data?.uuid || '',
    ma_vv: row.ma_vv_data?.uuid || '',
    ma_hd: row.ma_hd_data?.uuid || '',
    ma_dtt: row.ma_dtt_data?.uuid || '',
    ma_ku: row.ma_ku_data?.uuid || '',
    ma_phi: row.ma_phi_data?.uuid || '',
    ma_sp: row.ma_sp_data?.uuid || '',
    ma_lsx: row.ma_lsx_data?.uuid || '',
    ma_cp0: row.ma_cp0_data?.uuid || '',
    id_tt: parseInt(row.id_tt) || 0
  }));
};

/**
 * Transform tax rows for PhieuThanhToanTamUng API submission
 * @param taxRows - Array of tax row data from the form
 * @returns Transformed tax rows ready for API submission
 */
export const transformTaxRows = (taxRows: any[]) => {
  return taxRows.map((row: any, index: number) => ({
    line: index + 1,
    so_ct0: row.so_ct0 || '',
    so_ct2: row.so_ct2 || '',
    ngay_ct0: row.ngay_ct0 || '',
    ma_thue: row.ma_thue_data?.uuid || '',
    ten_thue: row.ma_thue_data?.ten_thue || '',
    thue_suat: parseFloat(row.ma_thue_data?.thue_suat) || 0,
    ma_mau_ct: row.ma_mau_ct_data?.ma_mau_so || row.ma_mau_ct || '',
    ma_mau_bc: row.ma_mau_bc || '',
    ma_tc_thue: row.ma_tc_thue_data?.uuid || row.ma_tc_thue || '',
    ma_kh: row.ma_kh_data?.uuid || row.ma_kh || '',
    ten_kh_thue: row.ma_kh_data?.customer_name || row.ten_kh_thue || '',
    dia_chi: row.dia_chi || row.ma_kh_data?.address || '',
    ma_so_thue: row.ma_kh_data?.tax_code || row.ma_so_thue || '',
    ten_vt_thue: row.ten_vt_thue || '',
    t_tien_nt: parseFloat(row.t_tien_nt) || 0,
    t_tien: parseFloat(row.t_tien) || 0,
    tk_thue_no: row.tk_thue_no_data?.uuid || '',
    ten_tk_thue_no: row.tk_thue_no_data?.name || row.ten_tk_thue_no || '',
    tk_du: row.tk_du_data?.uuid || '',
    ten_tk_du: row.tk_du_data?.name || row.ten_tk_du || '',
    t_thue_nt: parseFloat(row.t_thue_nt) || 0,
    t_thue: parseFloat(row.t_thue) || 0,
    ma_kh9: row.ma_kh9_data?.uuid || '',
    ten_kh9: row.ma_kh9_data?.customer_name || '',
    ma_tt: row.ma_tt_data?.uuid || '',
    ten_tt: row.ma_tt_data?.ten_tt || '',
    ghi_chu: row.ghi_chu || '',
    id_tt: parseInt(row.id_tt) || 0,
    ma_bp: row.ma_bp_data?.uuid || '',
    ma_vv: row.ma_vv_data?.uuid || '',
    ma_hd: row.ma_hd_data?.uuid || '',
    ma_dtt: row.ma_dtt_data?.uuid || '',
    ma_ku: row.ma_ku_data?.uuid || '',
    ma_phi: row.ma_phi_data?.uuid || '',
    ma_sp: row.ma_sp_data?.uuid || '',
    ma_lsx: row.ma_lsx_data?.uuid || '',
    ma_cp0: row.ma_cp0_data?.uuid || ''
  }));
};

/**
 * Transform advance settlement rows for PhieuThanhToanTamUng API submission
 * @param advSettlementRows - Array of advance settlement row data from the form
 * @returns Transformed settlement rows ready for API submission
 */
export const transformAdvSettlementRows = (advSettlementRows: any[]) => {
  return advSettlementRows.map((row: any, index: number) => ({
    line: index + 1,
    ma_kh: row.ma_kh_data?.uuid || row.ma_kh || '',
    id_chi: row.id_chi || '',
    tien_nt: parseFloat(row.tien_nt) || 0,
    tien: parseFloat(row.tien) || 0
  }));
};

/**
 * Smart merge function that prioritizes state data over form data and falls back to initial data
 * @param formData - Data from form submission
 * @param stateData - Data from form field state
 * @param initialData - Initial data for edit mode
 * @returns Merged value with proper priority
 */
const merge = (formData: any, stateData: any, initialData: any) => {
  // Priority: stateData > formData > initialData
  if (stateData !== undefined && stateData !== null && stateData !== '') {
    return stateData;
  }
  if (formData !== undefined && formData !== null && formData !== '') {
    return formData;
  }
  return initialData || '';
};

/**
 * Extract entity UUID from state or initial data
 * @param stateEntity - Entity from form state
 * @param initialField - Field from initial data
 * @returns Entity UUID or empty string
 */
const getEntityUUID = (stateEntity: any, initialField: any) => {
  if (stateEntity?.uuid) return stateEntity.uuid;
  if (typeof initialField === 'string') return initialField;
  if (initialField?.uuid) return initialField.uuid;
  return '';
};

/**
 * Transform all form data for PhieuThanhToanTamUng submission
 * Enhanced version that handles edit mode efficiently with fewer props
 * @param options - Configuration object containing all necessary data
 * @returns Transformed data ready for API submission
 */
export const transformFormData = (options: {
  data: any;
  state: FormFieldState;
  detailRows?: any[];
  taxRows?: any[];
  advSettlementRows?: any[];
  entityUnit?: any;
  initialData?: any;
  formMode?: 'create' | 'edit' | 'view';
  includeEmptyArrays?: boolean; // Option to control whether to include empty arrays
}) => {
  const {
    data,
    state,
    detailRows = [],
    taxRows = [],
    advSettlementRows = [],
    entityUnit,
    initialData = {},
    formMode = 'create',
    includeEmptyArrays = false // Default to not including empty arrays for cleaner payload
  } = options;

  const chi_tiet_phieu_thanh_toan = transformDetailRows(detailRows);
  const thue_phieu_thanh_toan = transformTaxRows(taxRows);
  const quyet_toan_tam_ung = transformAdvSettlementRows(advSettlementRows);

  const isEditMode = formMode === 'edit' && initialData;

  // Handle document number transformation based on mode
  const documentNumberData = isEditMode
    ? {
        ma_nk: getEntityUUID(state.quyenChungTu, initialData.ma_nk_data?.uuid || initialData.ma_nk),
        so_ct: state.soChungTu || initialData.so_ct || '',
        i_so_ct: state.quyenChungTu
          ? transformDocumentNumber(state.quyenChungTu, state.soChungTu, MA_CHUNG_TU.MUA_HANG.PHIEU_THANH_TOAN_TAM_UNG)
              .i_so_ct
          : initialData.i_so_ct || '0'
      }
    : transformDocumentNumber(state.quyenChungTu, state.soChungTu, MA_CHUNG_TU.MUA_HANG.PHIEU_THANH_TOAN_TAM_UNG);

  const transformedData: any = {
    ma_ngv: merge(data.ma_ngv, null, initialData.ma_ngv),
    ma_kh: getEntityUUID(state.khachHang, initialData.ma_kh),
    ma_so_thue: merge(data.ma_so_thue, state.khachHang?.tax_code, initialData.ma_so_thue),
    ong_ba: merge(data.ong_ba, state.khachHang?.contact_person, initialData.ong_ba),
    dia_chi: merge(data.dia_chi, state.khachHang?.address, initialData.dia_chi),
    e_mail: merge(data.e_mail, state.khachHang?.email, initialData.e_mail),
    tk: getEntityUUID(state.taiKhoan, initialData.tk),
    dien_giai: merge(data.dien_giai, state.dienGiai, initialData.dien_giai),
    unit_id: entityUnit?.uuid || initialData.unit_id || '',
    ...documentNumberData,
    ngay_ct: data.ngay_ct
      ? format(new Date(data.ngay_ct), 'yyyy-MM-dd')
      : initialData.ngay_ct
        ? format(new Date(initialData.ngay_ct), 'yyyy-MM-dd')
        : '',
    ngay_lct: data.ngay_lct
      ? format(new Date(data.ngay_lct), 'yyyy-MM-dd')
      : data.ngay_ct
        ? format(new Date(data.ngay_ct), 'yyyy-MM-dd')
        : initialData.ngay_lct
          ? format(new Date(initialData.ngay_lct), 'yyyy-MM-dd')
          : '',
    ma_nt: merge(data.ma_nt, null, initialData.ma_nt) || 'VND',
    ty_gia: parseFloat(data.ty_gia) || parseFloat(initialData.ty_gia) || 1,
    status: merge(data.status, null, initialData.status) || 'active',
    transfer_yn: Boolean(data.transfer_yn ?? initialData.transfer_yn),
    t_tien_nt: parseFloat(data.t_tien_nt) || parseFloat(initialData.t_tien_nt) || 0,
    t_tien: parseFloat(data.t_tien) || parseFloat(initialData.t_tien) || 0,
    t_thue_nt: parseFloat(data.t_thue_nt) || parseFloat(initialData.t_thue_nt) || 0,
    t_thue: parseFloat(data.t_thue) || parseFloat(initialData.t_thue) || 0,
    t_tt_nt: parseFloat(data.t_tt_nt) || parseFloat(initialData.t_tt_nt) || 0,
    t_tt: parseFloat(data.t_tt) || parseFloat(initialData.t_tt) || 0,
    t_tien_cl_nt: parseFloat(data.t_tien_cl_nt) || parseFloat(initialData.t_tien_cl_nt) || 0,
    t_tien_cl: parseFloat(data.t_tien_cl) || parseFloat(initialData.t_tien_cl) || 0,
    qt_tu_yn: Boolean(state.quyetToanTamUng ?? initialData.qt_tu_yn)
  };

  // Conditionally include nested data fields based on options
  // This reduces payload size when no detail data is provided
  if (chi_tiet_phieu_thanh_toan.length > 0 || isEditMode || includeEmptyArrays) {
    transformedData.chi_tiet_phieu_thanh_toan = chi_tiet_phieu_thanh_toan;
  }

  if (thue_phieu_thanh_toan.length > 0 || isEditMode || includeEmptyArrays) {
    transformedData.thue_phieu_thanh_toan = thue_phieu_thanh_toan;
  }

  if (quyet_toan_tam_ung.length > 0 || isEditMode || includeEmptyArrays) {
    transformedData.quyet_toan_tam_ung = quyet_toan_tam_ung;
  }

  if (isEditMode) {
    const preservedFields = ['uuid', 'created', 'updated', 'record_id', 'inv_id'];
    preservedFields.forEach(field => {
      if (initialData[field] !== undefined) {
        transformedData[field] = initialData[field];
      }
    });
  }

  return transformedData;
};
