import { GridCellParams } from '@mui/x-data-grid';
import { useState } from 'react';

export interface TaxRow {
  uuid?: string | null;
  [key: string]: any;
}

export interface SelectedTaxCellInfo {
  id: string;
  field: string;
}

export function useTaxRows(initialRows: TaxRow[] = []) {
  const [rows, setRows] = useState<TaxRow[]>(initialRows);
  const [selectedRowUuid, setSelectedRowUuid] = useState<string | null>(null);
  const [selectedRow, setSelectedRow] = useState<TaxRow | null>(null);
  const [selectedCell, setSelectedCell] = useState<SelectedTaxCellInfo | null>(null);

  const handleAddRow = () => {
    const newRow = {
      uuid: String(Math.random())
    };

    setRows([...rows, newRow]);

    // Select the newly added row
    setSelectedRowUuid(newRow.uuid);
    setSelectedRow(newRow);
  };

  const handleDeleteRow = () => {
    let updatedRows: TaxRow[] = [];

    if (selectedRowUuid) {
      updatedRows = rows.filter(row => row.uuid !== selectedRowUuid);
      setRows(updatedRows);
    } else if (rows.length > 0) {
      updatedRows = rows.slice(0, -1);
      setRows(updatedRows);
    }

    // If there are remaining rows, select the last one
    if (updatedRows.length > 0) {
      const lastRow = updatedRows[updatedRows.length - 1];
      setSelectedRowUuid(lastRow.uuid || null);
      setSelectedRow(lastRow);
    } else {
      // If no rows remain, clear selection
      clearSelection();
    }
  };

  const handleCopyRow = () => {
    if (!selectedRow) return;

    const { uuid, ...rowDataWithoutUuid } = selectedRow;

    const newRow = {
      ...rowDataWithoutUuid,
      uuid: String(Math.random())
    };

    setRows([...rows, newRow]);

    // Select the newly copied row
    setSelectedRowUuid(newRow.uuid);
    setSelectedRow(newRow);
  };

  const handlePasteRow = () => {
    if (!selectedRow) return;

    const { uuid, ...rowDataWithoutUuid } = selectedRow;

    let newRow = {
      ...rowDataWithoutUuid,
      uuid: String(Math.random())
    };

    if (selectedRowUuid) {
      const selectedIndex = rows.findIndex(row => row.uuid === selectedRowUuid);
      if (selectedIndex === -1) return;

      const newRows = [...rows];
      newRows.splice(selectedIndex + 1, 0, newRow);

      setRows(newRows);
    } else {
      setRows([...rows, newRow]);
    }

    // Select the newly pasted row
    setSelectedRowUuid(newRow.uuid);
    setSelectedRow(newRow);
  };

  const handleMoveRow = (direction: 'up' | 'down') => {
    if (!selectedRowUuid) return;

    const currentIndex = rows.findIndex(row => row.uuid === selectedRowUuid);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? Math.max(0, currentIndex - 1) : Math.min(rows.length - 1, currentIndex + 1);

    if (newIndex === currentIndex) return;

    const newRows = [...rows];
    const [movedRow] = newRows.splice(currentIndex, 1);
    newRows.splice(newIndex, 0, movedRow);

    setRows(newRows);

    // Ensure selection follows the moved row
    // No need to update selectedRowUuid as it remains the same
    setSelectedRow(movedRow);
  };

  const handleCellValueChange = (rowUuid: string, field: string, newValue: any) => {
    const rowIndex = rows.findIndex(row => row.uuid === rowUuid);
    if (rowIndex === -1) return;

    const updatedRows = [...rows];
    const currentRow = updatedRows[rowIndex];

    updatedRows[rowIndex] = {
      ...currentRow,
      [field]: newValue
    };

    // Calculate tien_nt0 when gia_nt0 or so_luong changes
    if (field === 'gia_nt0') {
      const so_luong = Number(currentRow.so_luong) || 1;
      const gia_nt0 = Number(newValue) || 0;
      const tien_nt0 = gia_nt0 * so_luong;

      updatedRows[rowIndex] = {
        ...updatedRows[rowIndex],
        tien_nt0: tien_nt0
      };
    } else if (field === 'so_luong') {
      const gia_nt0 = Number(currentRow.gia_nt0) || 0;
      const so_luong = Number(newValue) || 1;
      const tien_nt0 = gia_nt0 * so_luong;

      updatedRows[rowIndex] = {
        ...updatedRows[rowIndex],
        tien_nt0: tien_nt0
      };
    }

    // Calculate tax (thue_nt) when relevant fields change
    const shouldCalculateTax = ['tien_nt0', 'gia_nt0', 'ma_thue_data'].includes(field);
    if (shouldCalculateTax) {
      const updatedRow = updatedRows[rowIndex];
      const baseAmount = updatedRow.tien_nt0 || (updatedRow.gia_nt0 || 0) * (updatedRow.so_luong || 0) || 0;
      const taxRate = updatedRow.ma_thue_data?.thue_suat || 0;
      const calculatedTax = (baseAmount * taxRate) / 100;

      updatedRows[rowIndex] = {
        ...updatedRows[rowIndex],
        thue_nt: calculatedTax
      };
    }

    setRows(updatedRows);

    // If the changed row is the currently selected row, update selectedRow
    if (rowUuid === selectedRowUuid) {
      setSelectedRow(updatedRows[rowIndex]);
    }
  };

  const handleRowClick = (params: { id: string; row: TaxRow }) => {
    const rowUuid = params.id || params.row?.uuid;
    if (!rowUuid) return;

    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row);
  };

  const handleCellClick = (params: GridCellParams) => {
    const rowUuid = params.id.toString();
    if (!rowUuid) return;

    // Update row selection
    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row as TaxRow);

    // Update cell selection
    setSelectedCell({
      id: rowUuid,
      field: params.field
    });
  };

  const clearSelection = () => {
    setSelectedRowUuid(null);
    setSelectedRow(null);
    setSelectedCell(null);
  };

  return {
    rows,
    setRows,
    selectedRowUuid,
    selectedRow,
    selectedCell,
    handleRowClick,
    handleCellClick,
    clearSelection,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange
  };
}
