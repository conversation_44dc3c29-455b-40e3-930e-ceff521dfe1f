import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';
import type { ApiResponse } from '@/types/api.type';

// Generic interfaces for reusable data hook
interface UseDataConfig {
  endpoint: string;
  searchData?: any;
  pageSize?: number;
}

interface UseDataReturn<TData, TInput> {
  data: TData[];
  isLoading: boolean;
  getReport: (data: TInput) => Promise<void>;
}

/**
 * Generic reusable data hook for CRUD operations
 * @param config - Configuration object with endpoint and optional queryKey
 * @param initialList - Initial data list (optional)
 * @param searchData - Search parameters (optional)
 * @returns Hook return object with data and CRUD operations
 */
export const useReport = <TData extends { uuid: string }, TInput = Partial<TData>>(
  config: UseDataConfig,
  initialList?: TData[]
): UseDataReturn<TData, TInput> => {
  const [data, setData] = useState<TData[]>(initialList || []);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState('');
  const { entity } = useAuth();
  const { endpoint, searchData } = config;

  const getReport = async (itemData: TInput): Promise<void> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      const response = await api.post<ApiResponse<TData>>(`/entities/${entity.slug}/erp/${endpoint}/`, itemData, {
        params: searchData
      });
      setData(response.data.results);
    } catch (error: any) {
      setError(error?.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    data,
    isLoading,
    getReport
  };
};
