import { FormField } from '@/components/custom/arito';

export const DetailTab = () => {
  return (
    <div className='h-32 p-4'>
      <div className='space-y-2'>
        {/* Classification Select Field */}
        <FormField
          type='select'
          label='Phân loại'
          name='phan_loai'
          options={[
            { value: 1, label: 'Chi tiết' },
            { value: 2, label: 'Nhóm theo tài khoản đối ứng' },
            { value: 3, label: 'Nhóm theo chứng từ' }
          ]}
          labelClassName='w-32'
          inputClassName='min-w-40'
        />

        {/* Report Template Select Field */}
        <FormField
          type='select'
          label='Mẫu báo cáo'
          name='mau_bc'
          options={[
            { value: 20, label: 'Mẫu tiền chuẩn' },
            { value: 30, label: 'Mẫu ngoại tệ' }
          ]}
          labelClassName='w-32'
          inputClassName='min-w-40'
        />
      </div>
    </div>
  );
};

export default DetailTab;
