import { formatYyyyMmDd } from '@/lib/utils';
import { SearchFormValues } from '../schema';

/**
 * Transform search form data for API submission
 * @param formData - Form data from the search form
 * @returns Transformed data ready for API request
 */
export const transformSearchData = (formData: SearchFormValues) => {
  // Destructure to remove _data fields from payload
  const { ma_lts_data, ma_bp_data, nh_ts1_data, nh_ts2_data, nh_ts3_data, ...cleanFormData } = formData;

  return {
    ...cleanFormData,
    ngay_bc: formData.ngay_bc ? formatYyyyMmDd(new Date(formData.ngay_bc)).replace(/-/g, '') : '',

    // Transform _data objects to uuid fields
    ma_lts: ma_lts_data?.uuid || '',
    ma_bp: ma_bp_data?.uuid || '',
    nh_ts1: nh_ts1_data?.uuid || '',
    nh_ts2: nh_ts2_data?.uuid || '',
    nh_ts3: nh_ts3_data?.uuid || ''
  };
};

/**
 * Transform API response data if needed
 * @param responseData - Raw response data from API
 * @returns Transformed response data
 */
export const transformResponseData = (responseData: any) => {
  // Add any response data transformation logic here if needed
  return responseData;
};
