import { useState, useCallback } from 'react';
import { transformSearchData, transformResponseData } from '../utils/transform-data';
import { useAuth } from '@/contexts/auth-context';
import { SearchFormValues } from '../schema';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

interface UseBangKeTSCDHetKhauHaoReturn {
  data: any[];
  isLoading: boolean;
  error: string | null;
  submitSearch: (formData: SearchFormValues) => Promise<void>;
  clearData: () => void;
}

/**
 * Hook for managing Bang Ke TSCD Het Khau Hao Con Su Dung data
 */
export const useBangKeTSCDHetKhauHao = (): UseBangKeTSCDHetKhauHaoReturn => {
  const { entity } = useAuth();
  const [data, setData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Submit search form and fetch data
   */
  const submitSearch = useCallback(
    async (formData: SearchFormValues) => {
      if (!entity?.slug) {
        setError('Entity not found');
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Transform form data to API format
        const requestBody = transformSearchData(formData);

        // Call API endpoint
        const response = await api.post(
          `/entities/${entity.slug}/erp/${QUERY_KEYS.BANG_KE_TSCD_HET_KHAU_HAO_CON_SU_DUNG}/`,
          requestBody
        );

        // Transform response data if needed
        const transformedData = transformResponseData(response.data);

        // Set data - safely handle response structure
        if (transformedData && Array.isArray(transformedData.results)) {
          setData(transformedData.results);
        } else if (Array.isArray(transformedData)) {
          setData(transformedData);
        } else {
          setData([]);
        }
      } catch (err: any) {
        const errorMessage = err.response?.data?.message || err.message || 'Có lỗi xảy ra khi tìm kiếm';
        setError(errorMessage);
        console.error('Error submitting search:', err);
        setData([]);
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug]
  );

  /**
   * Clear current data and error state
   */
  const clearData = useCallback(() => {
    setData([]);
    setError(null);
  }, []);

  return {
    data,
    isLoading,
    error,
    submitSearch,
    clearData
  };
};
