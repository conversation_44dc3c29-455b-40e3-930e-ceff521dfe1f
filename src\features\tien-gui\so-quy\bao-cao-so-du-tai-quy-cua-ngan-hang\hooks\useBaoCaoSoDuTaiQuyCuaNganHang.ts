import { useState, useCallback } from 'react';
import type { BaoCaoSoDuTaiQuyCuaNganHangItem, BaoCaoSoDuTaiQuyCuaNganHangResponse, SearchFormValues } from '../schema';
import { QUERY_KEYS } from '@/constants/query-keys';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

export interface UseBaoCaoSoDuTaiQuyCuaNganHangReturn {
  data: BaoCaoSoDuTaiQuyCuaNganHangItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
  submitBaoCaoSoDuTaiQuyCuaNganHang: (requestBody: any) => Promise<void>;
}

/**
 * Custom hook for managing BaoCaoSoDuTaiQuyCuaNganHang (Bank Cash Balance Report) data
 */
export function useBaoCaoSoDuTaiQuyCuaNganHang(searchParams: SearchFormValues): UseBaoCaoSoDuTaiQuyCuaNganHangReturn {
  const { entity } = useAuth();
  const [data, setData] = useState<BaoCaoSoDuTaiQuyCuaNganHangItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(
    async (searchParams: SearchFormValues) => {
      if (!entity?.slug) {
        setError(new Error('Entity not found'));
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await api.post<BaoCaoSoDuTaiQuyCuaNganHangResponse>(
          `/entities/${entity.slug}/erp/${QUERY_KEYS.BAO_CAO_SO_DU_TAI_QUY_CUA_NGAN_HANG}/`,
          searchParams
        );

        // Handle response data structure
        if (response.data && response.data.results) {
          setData(response.data.results);
        } else if (response.data && Array.isArray(response.data)) {
          setData(response.data as BaoCaoSoDuTaiQuyCuaNganHangItem[]);
        } else {
          setData([]);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
        setError(new Error(errorMessage));
        setData([]);
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug]
  );

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  const submitBaoCaoSoDuTaiQuyCuaNganHang = useCallback(
    async (requestBody: any) => {
      if (!entity?.slug) {
        setError(new Error('Entity not found'));
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        await api.post(`/entities/${entity.slug}/erp/${QUERY_KEYS.BAO_CAO_SO_DU_TAI_QUY_CUA_NGAN_HANG}/`, requestBody);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while submitting data';
        setError(new Error(errorMessage));
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug]
  );

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData,
    submitBaoCaoSoDuTaiQuyCuaNganHang
  };
}
