import { GridCellParams, GridColDef } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  accountSearchColumns,
  boPhanSearchColumns,
  chiPhiKhongHopLeSearchColumns,
  chiPhiSearchColumns,
  dotThanhToanSearchColumns,
  hopDongSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  vuViecSearchColumns,
  customerSearchColumns,
  thueSearchColumns,
  mauSoHoaDonSearchColumns
} from '@/constants';
import {
  AccountModel,
  BoPhan,
  ChiPhi,
  ChiPhiKhongHopLeData,
  DotThanhToan,
  HopDong,
  KheUoc,
  Phi,
  VatTu,
  VuViec,
  KhachHang,
  Tax,
  MauSoHoaDon
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

export const getDetailTableColumns = (
  loaiHD: string,
  setLoaiHD: (value: string) => void,
  maNgv: string,
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => {
  const baseColumns: GridColDef[] = [
    // tk_no - Tài khoản nợ
    {
      field: 'tk_no',
      headerName: 'Tài khoản nợ',
      width: 120,
      renderCell: params => (
        <SearchField<AccountModel>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
          searchColumns={accountSearchColumns}
          columnDisplay='code'
          dialogTitle='Danh mục tài khoản'
          value={params.row.tk_no_data?.code || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_no_data', row)}
        />
      )
    },
    // ten_tk - Tên tài khoản
    {
      field: 'ten_tk',
      headerName: 'Tên tài khoản',
      width: 250,
      renderCell: params => <CellField name='ten_tk' type='text' value={params.row.tk_no_data?.name || ''} />
    },
    // ma_kh - Mã khách hàng (always visible)
    {
      field: 'ma_kh',
      headerName: 'Mã khách hàng',
      width: 120,
      renderCell: (params: GridCellParams) => (
        <SearchField<KhachHang>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
          searchColumns={customerSearchColumns}
          columnDisplay='customer_code'
          dialogTitle='Danh mục khách hàng'
          value={params.row.ma_kh_data?.customer_code || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh_data', row)}
        />
      )
    },
    // ten_kh - Tên khách hàng (always visible)
    {
      field: 'ten_kh',
      headerName: 'Tên khách hàng',
      width: 200,
      renderCell: (params: GridCellParams) => (
        <CellField name='ten_kh' type='text' value={params.row.ma_kh_data?.customer_name || ''} />
      )
    },
    // tien_nt - Tiền ngoại tệ
    {
      field: 'tien_nt',
      headerName: 'Tiền VND',
      width: 120,
      renderCell: params => (
        <CellField
          name='tien_nt'
          type='number'
          value={params.row.tien_nt || 0.0}
          onValueChange={newValue => {
            onCellValueChange(params.row.uuid, 'tien_nt', newValue);
          }}
        />
      )
    },
    // dien_giai - Diễn giải
    {
      field: 'dien_giai',
      headerName: 'Diễn giải',
      width: 200,
      renderCell: params => (
        <CellField
          name='dien_giai'
          type='text'
          value={params.row.dien_giai || ''}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'dien_giai', newValue)}
        />
      )
    },
    // ma_loai_hd - Mã loại hóa đơn
    {
      field: 'ma_loai_hd',
      headerName: 'Loại hoá đơn',
      width: 120,
      renderCell: params => (
        <CellField
          name='ma_loai_hd'
          type='select'
          options={[
            { value: '0', label: '0. Không có hoá đơn' },
            { value: '1', label: '1. Hoá đơn GTGT đã tách thuế' },
            { value: '2', label: '2. Hoá đơn GTGT không tách thuế' },
            { value: '3', label: '3. Hoá đơn bán hàng thông thường' }
          ]}
          value={params.row.ma_loai_hd || '0'}
          onValueChange={newValue => {
            setLoaiHD(newValue);
            onCellValueChange(params.row.uuid, 'ma_loai_hd', newValue);
          }}
        />
      )
    },
    ...(loaiHD !== '0'
      ? [
          {
            field: 'ma_thue',
            headerName: 'Mã thuế',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <SearchField<Tax>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.THUE}/`}
                searchColumns={thueSearchColumns}
                columnDisplay='ma_thue'
                dialogTitle='Danh mục thuế'
                value={params.row.ma_thue_data?.ma_thue || ''}
                onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_thue_data', row)}
              />
            )
          },
          // ten_thue - Tên thuế
          {
            field: 'ten_thue',
            headerName: 'Tên thuế',
            width: 150,
            renderCell: (params: GridCellParams) => (
              <CellField name='ten_thue' type='text' value={params.row.ma_thue_data?.ten_thue || ''} />
            )
          },
          {
            field: 'thue_suat',
            headerName: 'Thuế suất',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <CellField name='thue_suat' type='number' value={params.row.ma_thue_data?.thue_suat || 0.0} />
            )
          },
          {
            field: 'tk_thue',
            headerName: 'Tk thuế',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <SearchField<AccountModel>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
                searchColumns={accountSearchColumns}
                columnDisplay='code'
                dialogTitle='Danh mục tài khoản'
                value={params.row.tk_thue_data?.code || ''}
                onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_thue_data', row)}
              />
            )
          },
          // ten_tk_thue - Tên tài khoản thuế
          {
            field: 'ten_tk_thue',
            headerName: 'Tên TK thuế',
            width: 150,
            renderCell: (params: GridCellParams) => (
              <CellField name='ten_tk_thue' type='text' value={params.row.tk_thue_data?.name || ''} />
            )
          },
          {
            field: 'so_ct0',
            headerName: 'Số hoá đơn',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='so_ct0'
                type='text'
                value={params.row.so_ct0 || ''}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_ct0', newValue)}
              />
            )
          },
          {
            field: 'so_ct2',
            headerName: 'Ký hiệu',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='so_ct2'
                type='text'
                value={params.row.so_ct2 || ''}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_ct2', newValue)}
              />
            )
          },
          {
            field: 'ngay_ct0',
            headerName: 'Ngày hoá đơn',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='ngay_ct0'
                type='date'
                value={params.row.ngay_ct0 || ''}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'ngay_ct0', newValue)}
              />
            )
          },
          {
            field: 'ma_mau_ct',
            headerName: 'Mẫu HĐ',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <SearchField<MauSoHoaDon>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.MAU_SO_HOA_DON}/`}
                searchColumns={mauSoHoaDonSearchColumns}
                columnDisplay='ma_mau_so'
                dialogTitle='Danh mục mẫu hoá đơn'
                value={params.row.ma_mau_ct_data?.ma_mau_so || ''}
                onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_mau_ct_data', row)}
              />
            )
          },
          // ten_mau_ct - Tên mẫu chứng từ
          {
            field: 'ten_mau_ct',
            headerName: 'Tên mẫu HĐ',
            width: 150,
            renderCell: (params: GridCellParams) => (
              <CellField name='ten_mau_ct' type='text' value={params.row.ma_mau_ct_data?.ten_mau_so || ''} />
            )
          },
          // ma_mau_bc - Mã mẫu báo cáo
          {
            field: 'ma_mau_bc',
            headerName: 'Mẫu báo cáo',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='ma_mau_bc'
                type='select'
                options={[
                  { value: '3', label: '3. Hóa đơn giá trị gia tăng' },
                  { value: '4', label: '4. Hàng hóa, dịch vụ mua vào không có hóa đơn' },
                  { value: '5', label: '5. Hóa đơn bán hàng thông thường' }
                ]}
                value={params.row.ma_mau_bc || ''}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_mau_bc', newValue)}
              />
            )
          },
          // ma_tc_thue - Mã tính chất thuế
          {
            field: 'ma_tc_thue',
            headerName: 'Mã tính chất',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='ma_tc_thue'
                type='select'
                options={[
                  {
                    value: '1',
                    label:
                      '1. Hàng hoá, dịch vụ dùng riêng cho SXKD chịu thuế GTGT và sử dụng cho các hoạt động cung cấp hàng hoá, dịch vụ không kê khai, nộp thuế GTGT đủ điều kiện khấu trừ thuế'
                  },
                  {
                    value: '2',
                    label: '2. Hàng hoá, dịch vụ dùng chung cho SXKD chịu thuế GTGT và không chịu thuế GTGT'
                  },
                  { value: '3', label: '3. Hàng hóa, dịch vụ dùng cho dự án đầu tư đủ điều kiện khấu trừ thuế' },
                  { value: '4', label: '4. Hàng hóa, dịch vụ không đủ điều kiện khấu trừ' },
                  { value: '5', label: '5. Hàng hóa, dịch vụ không phải tổng hợp trên tờ khai 01/GTGT' }
                ]}
                value={params.row.ma_tc_thue || ''}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_tc_thue', newValue)}
              />
            )
          },
          // ma_kh_invoice - Mã khách hàng cho hóa đơn
          {
            field: 'ma_kh_thue',
            headerName: 'Mã ncc',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <SearchField<KhachHang>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
                searchColumns={customerSearchColumns}
                columnDisplay='customer_code'
                dialogTitle='Danh mục đối tượng'
                value={params.row.ma_kh_thue_data?.customer_code || ''}
                onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh_thue_data', row)}
              />
            )
          },
          // ten_kh_thue - Tên khách hàng thuế
          {
            field: 'ten_kh_thue',
            headerName: 'Tên nhà cung cấp',
            width: 150,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='ten_kh_thue'
                type='text'
                value={params.row.ma_kh_thue_data?.customer_name || params.row.ten_kh_thue || ''}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'ten_kh_thue', newValue)}
              />
            )
          },
          // dia_chi - Địa chỉ
          {
            field: 'dia_chi',
            headerName: 'Địa chỉ',
            width: 200,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='dia_chi'
                type='text'
                value={params.row.dia_chi || params.row.ma_kh_thue_data?.address || ''}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'dia_chi', newValue)}
              />
            )
          },
          // ma_so_thue - Mã số thuế
          {
            field: 'ma_so_thue',
            headerName: 'Mã số thuế',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='ma_so_thue'
                type='text'
                value={params.row.ma_so_thue || params.row.ma_kh_thue_data?.tax_code || ''}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_so_thue', newValue)}
              />
            )
          },
          // ten_vt_thue - Tên vật tư thuế
          {
            field: 'ten_vt_thue',
            headerName: 'Tên hàng hóa - dịch vụ',
            width: 150,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='ten_vt_thue'
                type='text'
                value={params.row.ten_vt_thue || ''}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'ten_vt_thue', newValue)}
              />
            )
          },
          // thue_nt - Thuế ngoại tệ
          {
            field: 'thue_nt',
            headerName: 'Thuế NT',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='thue_nt'
                type='number'
                value={params.row.thue_nt || 0.0}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'thue_nt', newValue)}
              />
            )
          },
          // ma_kh9 - Mã khách hàng 9
          {
            field: 'ma_kh9',
            headerName: 'Cục thuế',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <SearchField<KhachHang>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
                searchColumns={customerSearchColumns}
                columnDisplay='customer_code'
                dialogTitle='Danh mục đối tượng'
                value={params.row.ma_kh9_data?.customer_code || ''}
                onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh9_data', row)}
              />
            )
          }
        ]
      : []),
    // ma_bp - Mã bộ phận
    {
      field: 'ma_bp',
      headerName: 'Bộ phận',
      width: 120,
      renderCell: (params: GridCellParams) => (
        <SearchField<BoPhan>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
          searchColumns={boPhanSearchColumns}
          columnDisplay='ma_bp'
          dialogTitle='Danh mục bộ phận'
          value={params.row.ma_bp_data?.ma_bp || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_bp_data', row)}
        />
      )
    },
    // ma_vv - Mã vụ việc
    {
      field: 'ma_vv',
      headerName: 'Vụ việc',
      width: 120,
      renderCell: params => (
        <SearchField<VuViec>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
          searchColumns={vuViecSearchColumns}
          columnDisplay='ma_vu_viec'
          dialogTitle='Danh mục vụ việc'
          value={params.row.ma_vv_data?.ma_vv || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_vv_data', row)}
        />
      )
    },
    // ma_hd - Mã hợp đồng
    {
      field: 'ma_hd',
      headerName: 'Hợp đồng',
      width: 120,
      renderCell: params => (
        <SearchField<HopDong>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
          searchColumns={hopDongSearchColumns}
          columnDisplay='ma_hd'
          dialogTitle='Danh mục hợp đồng'
          value={params.row.ma_hd_data?.ma_hd || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_hd_data', row)}
        />
      )
    },
    // ma_dtt - Mã đợt thanh toán
    {
      field: 'ma_dtt',
      headerName: 'Đợt thanh toán',
      width: 120,
      renderCell: params => (
        <SearchField<DotThanhToan>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
          searchColumns={dotThanhToanSearchColumns}
          columnDisplay='ma_dtt'
          dialogTitle='Danh mục đợt thanh toán'
          value={params.row.ma_dtt_data?.ma_dtt || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_dtt_data', row)}
        />
      )
    },
    // ma_ku - Mã khế ước
    {
      field: 'ma_ku',
      headerName: 'Khế ước',
      width: 120,
      renderCell: params => (
        <SearchField<KheUoc>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
          searchColumns={kheUocSearchColumns}
          columnDisplay='ma_ku'
          dialogTitle='Danh mục khế ước'
          value={params.row.ma_ku_data?.ma_ku || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_ku_data', row)}
        />
      )
    },
    // ma_phi - Mã phí
    {
      field: 'ma_phi',
      headerName: 'Phí',
      width: 120,
      renderCell: params => (
        <SearchField<Phi>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.PHI}/`}
          searchColumns={phiSearchColumns}
          columnDisplay='ma_phi'
          dialogTitle='Danh mục phí'
          value={params.row.ma_phi_data?.ma_phi || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_phi_data', row)}
        />
      )
    },
    // ma_sp - Mã sản phẩm
    {
      field: 'ma_sp',
      headerName: 'Sản phẩm',
      width: 120,
      renderCell: params => (
        <SearchField<VatTu>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
          searchColumns={vatTuSearchColumns}
          columnDisplay='ma_vt'
          dialogTitle='Danh mục sản phẩm'
          value={params.row.ma_sp_data?.ma_vt || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_sp_data', row)}
        />
      )
    },
    // ma_lsx - Mã lệnh sản xuất
    {
      field: 'ma_lsx',
      headerName: 'Lệnh sản xuất',
      width: 120,
      renderCell: params => (
        <SearchField<any>
          type='text'
          searchEndpoint={'/'}
          searchColumns={lenhSanXuatSearchColumns}
          columnDisplay='ma_lsx'
          dialogTitle='Danh mục lệnh sản xuất'
          value={params.row.ma_lsx_data?.ma_lsx || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_lsx_data', row)}
        />
      )
    },
    // ma_cp0 - Mã chi phí không hợp lệ
    {
      field: 'ma_cp0',
      headerName: 'C/p không h/lệ',
      width: 120,
      renderCell: params => (
        <SearchField<ChiPhiKhongHopLeData>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
          searchColumns={chiPhiKhongHopLeSearchColumns}
          columnDisplay='ma_cp_khl'
          dialogTitle='Danh mục chi phí không hợp lệ'
          value={params.row.ma_cp0_data?.ma_cp || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_cp0_data', row)}
        />
      )
    }
  ];

  return baseColumns.filter(Boolean) as GridColDef[];
};
