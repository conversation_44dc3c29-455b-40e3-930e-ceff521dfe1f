import { useState, useEffect, useCallback } from 'react';
import { usePhieuKeToan } from '@/hooks';

interface UseDonHangMuaTrongNuocDetailReturn {
  detail: any[];
  isLoadingDetail: boolean;
  error: string | null;
  fetchDetail: () => Promise<void>;
  clearDetail: () => void;
}

/**
 * Custom hook for managing PhieuNhapChiPhiMuaHang detail data
 *
 * This hook handles fetching detail data for a selected PhieuNhapChiPhiMuaHang
 * and provides loading states and error handling.
 */
export const usePhieuKeToanDetail = (selectedUuid?: string): UseDonHangMuaTrongNuocDetailReturn => {
  const [detail, setDetail] = useState<any[]>([]);
  const [isLoadingDetail, setIsLoadingDetail] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { getPhieuKeToanDetail } = usePhieuKeToan();

  const fetchDetail = useCallback(async () => {
    // Clear previous data
    setDetail([]);
    setError(null);

    if (!selectedUuid) {
      setDetail([]);
      return;
    }

    console.log('Fetching detail for UUID:', selectedUuid);
    setIsLoadingDetail(true);

    try {
      const detailData = await getPhieuKeToanDetail(selectedUuid);
      setDetail(Array.isArray(detailData) ? detailData : []);
    } catch (error) {
      console.error('Error fetching detail:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Không thể tải chi tiết phiếu xuất trả lại nhà cung cấp';
      setError(errorMessage);
      setDetail([]);
    } finally {
      setIsLoadingDetail(false);
    }
  }, [selectedUuid, getPhieuKeToanDetail]);

  const clearDetail = useCallback(() => {
    setDetail([]);
    setError(null);
  }, []);

  // Auto-fetch when selectedUuid changes
  useEffect(() => {
    if (selectedUuid) {
      fetchDetail();
    } else {
      setDetail([]);
      setError(null);
    }
  }, [selectedUuid]); // Only depend on selectedUuid, not fetchDetail

  return {
    detail,
    isLoadingDetail,
    error,
    fetchDetail,
    clearDetail
  };
};
