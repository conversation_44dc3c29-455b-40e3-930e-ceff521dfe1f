'use client';

import React, { useMemo } from 'react';
import { useDialogState, useTableData, useActionHand<PERSON>, useBangKeTSCDHetKhauHao } from './hooks';
import EditPrintTemplateDialog from '@/components/custom/arito/edit-print-template';
import AritoDataTables from '@/components/custom/arito/data-tables';
import InitialSearchDialog from './components/InitialSearchDialog';
import { getDataTableColumns } from './cols-definition';
import { SearchFormValues } from './schema';
import { ActionBar } from './components';

export default function FullyDepreciatedAssetsReportPage() {
  const { data, isLoading, error, submitSearch, clearData } = useBangKeTSCDHetKhauHao();

  const {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,
    searchParams,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate
  } = useDialogState();

  const { tables, handleRowClick } = useTableData(searchParams);

  const { handleRefreshClick, handleFixedColumnsClick, handleExportDataClick } = useActionHandlers();

  const handleSearchSubmit = async (formData: SearchFormValues) => {
    try {
      await submitSearch(formData);
      // Close dialog and show table after successful API call
      handleInitialSearch(formData);
    } catch (error) {
      console.error('Error submitting search:', error);
      // Keep dialog open on error so user can retry
    }
  };

  const apiTables = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) return [];

    return [
      {
        name: 'Bảng kê TSCĐ hết khấu hao còn sử dụng',
        rows: data,
        columns: getDataTableColumns()
      }
    ];
  }, [data]);

  const displayTables = data.length > 0 ? apiTables : tables;

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleSearchSubmit}
      />

      <EditPrintTemplateDialog
        open={editPrintTemplateDialogOpen}
        onClose={handleClosePrintTemplateDialog}
        onSave={handleSavePrintTemplate}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportDataClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
            className='border-b border-gray-200'
            searchParams={searchParams}
          />

          <div className='flex-1 overflow-hidden'>
            {isLoading ? (
              <div className='flex h-full items-center justify-center'>
                <div className='text-center'>
                  <div className='mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600'></div>
                  <p className='text-gray-600'>Đang tải dữ liệu...</p>
                </div>
              </div>
            ) : error ? (
              <div className='flex h-full items-center justify-center'>
                <div className='text-center text-red-600'>
                  <p>Có lỗi xảy ra: {error}</p>
                </div>
              </div>
            ) : (
              <AritoDataTables tables={displayTables} onRowClick={handleRowClick} />
            )}
          </div>
        </>
      )}
    </div>
  );
}
