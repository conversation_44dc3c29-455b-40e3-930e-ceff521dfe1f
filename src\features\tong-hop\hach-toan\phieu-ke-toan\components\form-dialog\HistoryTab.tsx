import { GridColDef } from '@mui/x-data-grid';
import { AritoDataTables } from '@/components/custom/arito';
import { FormMode } from '@/types/form';
import { useRows } from '@/hooks';

interface HistoryTabProps {
  formMode: FormMode;
}

const historyColumns: GridColDef[] = [
  { field: 'username', headerName: 'Tài khoản', width: 100 },
  { field: 'nickname', headerName: 'Tên tài khoản', width: 200 },
  { field: 'action', headerName: 'Hành động', width: 100 },
  { field: 'datetime0', headerName: 'Thời gian', width: 150 }
];

export const HistoryTab = ({ formMode: _ }: HistoryTabProps) => {
  const { selectedRowIndex, handleRowClick } = useRows();

  const tables = [
    {
      name: '',
      rows: [],
      columns: historyColumns
    }
  ];

  return <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />;
};
