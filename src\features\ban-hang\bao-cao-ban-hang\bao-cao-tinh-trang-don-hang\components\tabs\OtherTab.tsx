import React, { useState } from 'react';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { giaoDichSearchColumns, loSearchColumns, viTriSearchColumns } from '@/constants';
import { SearchField, FormField } from '@/components/custom/arito';
import SaveTemplateDialog from '../SaveTemplateDialog';
import { QUERY_KEYS } from '@/constants/query-keys';
import { useSearchFieldStates } from '../../hooks';
import { Label } from '@/components/ui/label';

interface OtherTabProps {
  searchFieldStates?: ReturnType<typeof useSearchFieldStates>;
}

const OtherTab: React.FC<OtherTabProps> = ({ searchFieldStates }) => {
  const [saveFilterTemplateDialogOpen, setSaveFilterTemplateDialogOpen] = useState(false);

  const handleSaveFilterTemplate = () => {
    setSaveFilterTemplateDialogOpen(false);
  };

  return (
    <div className='space-y-4 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Loại đơn hàng:</Label>
          <div className='w-1/2'>
            <FormField
              name='ma_ngv'
              type='select'
              options={[
                { value: '__all__', label: '9.Tất cả' },
                { value: '5', label: 'Đơn hàng' },
                { value: '4', label: 'Hợp đồng' }
              ]}
              className='w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã giao dịch:</Label>
          <div>
            <SearchField
              type='text'
              searchEndpoint={`/`}
              searchColumns={giaoDichSearchColumns}
              dialogTitle='Giao dịch'
              columnDisplay='transactionCode'
              displayRelatedField='transactionName'
              value={searchFieldStates?.transaction?.transactionCode || ''}
              relatedFieldValue={searchFieldStates?.transaction?.transactionName || ''}
              onRowSelection={searchFieldStates?.setTransaction}
              className='w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã lô:</Label>
          <div>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.LO}/`}
              searchColumns={loSearchColumns}
              dialogTitle='Lô hàng'
              columnDisplay='ma_lo'
              displayRelatedField='ten_lo'
              value={searchFieldStates?.lot?.ma_lo || ''}
              relatedFieldValue={searchFieldStates?.lot?.ten_lo || ''}
              onRowSelection={searchFieldStates?.setLot}
              className='w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã vị trí:</Label>
          <div>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VI_TRI_KHO_HANG}/`}
              searchColumns={viTriSearchColumns}
              dialogTitle='Vị trí'
              columnDisplay='ma_vi_tri'
              displayRelatedField='ten_vi_tri'
              value={searchFieldStates?.location?.ma_vi_tri || ''}
              relatedFieldValue={searchFieldStates?.location?.ten_vi_tri || ''}
              onRowSelection={searchFieldStates?.setLocation}
              className='w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Diễn giải:</Label>
          <div className='w-2/3'>
            <FormField name='dien_giai' type='text' className='w-full' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mẫu lọc báo cáo:</Label>
          <div className='w-full'>
            <div className='flex items-center gap-1'>
              <div className='w-64'>
                <FormField
                  name='report_filtering'
                  type='select'
                  options={[{ value: '__all__', label: 'Người dùng tự lọc' }]}
                  className='w-full'
                />
              </div>

              <div className='h-9 w-9 flex-shrink-0'>
                <RadixHoverDropdown
                  iconNumber={624}
                  items={[
                    {
                      value: 'save_new',
                      label: 'Lưu mẫu mới',
                      icon: 7,
                      onClick: () => setSaveFilterTemplateDialogOpen(true)
                    },
                    {
                      value: 'save_overwrite',
                      label: 'Lưu đè vào mẫu đang chọn',
                      icon: 75,
                      onClick: () => console.log('Overwrite current filter template')
                    },
                    {
                      value: 'delete',
                      label: 'Xóa mẫu đang chọn',
                      icon: 8,
                      onClick: () => console.log('Delete current filter template')
                    }
                  ]}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Save Filter Template Dialog */}
      <SaveTemplateDialog
        open={saveFilterTemplateDialogOpen}
        onClose={() => setSaveFilterTemplateDialogOpen(false)}
        onSave={handleSaveFilterTemplate}
        templateType='filter'
      />
    </div>
  );
};

export default OtherTab;
