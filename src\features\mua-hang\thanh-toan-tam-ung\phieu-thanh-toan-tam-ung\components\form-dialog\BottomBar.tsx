import { FormFieldActions, FormFieldState } from '../../hooks';
import { FormField } from '@/components/custom/arito';
import { Checkbox } from '@/components/ui/checkbox';
import { formatMoney } from '@/lib/formatUtils';
import { Label } from '@/components/ui/label';

interface BottomBarProps {
  totalAmount?: number;
  totalTax?: number;
  totalPayment?: number;
  formState: {
    state: FormFieldState;
    actions: FormFieldActions;
  };
}

function BottomBar({ totalAmount = 0, totalTax = 0, totalPayment = 0, formState: { state, actions } }: BottomBarProps) {
  return (
    <div className='w-full border-t bg-white px-4 py-2'>
      <div className='flex'>
        <div className='ml-4 flex flex-col'>
          <div className='flex items-center'>
            <Label className='w-32 font-medium'>Tổng tiền</Label>
            <div className='w-[120px] pb-1'>
              <FormField
                name='t_tien_nt'
                type='text'
                disabled={true}
                labelClassName='mr-2 font-medium'
                inputClassName='text-right'
                value={formatMoney(totalAmount)}
              />
            </div>
            <FormField
              name='t_tien'
              type='text'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatMoney(totalAmount)}
              className='hidden'
            />
          </div>
          <div className='flex items-center'>
            <Label className='w-32 font-medium'>Tổng thuế</Label>
            <div className='w-[120px] pb-1'>
              <FormField
                name='t_thue_nt'
                type='text'
                disabled={true}
                labelClassName='mr-2 font-medium'
                inputClassName='text-right'
                value={formatMoney(totalTax)}
              />
            </div>
            <FormField
              name='t_thue'
              type='text'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatMoney(totalTax)}
              className='hidden'
            />
          </div>
          <div className='flex items-center'>
            <Label className='w-32 font-medium'>Tổng thanh toán</Label>
            <div className='w-[120px] pb-1'>
              <FormField
                name='t_tt_nt'
                type='text'
                disabled={true}
                labelClassName='mr-2 font-medium'
                inputClassName='text-right'
                value={formatMoney(totalPayment)}
              />
            </div>
            <FormField
              name='t_tt'
              type='text'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatMoney(totalPayment)}
              className='hidden'
            />
          </div>
        </div>

        <div className='ml-auto flex w-1/4 flex-col'>
          {/* Column 3 */}
          <div className='flex items-center'>
            <Checkbox name='qt_tu_yn' onCheckedChange={actions.setQuyetToanTamUng} />
            <Label className='ml-2 font-medium'>Quyết toán các lần tạm ứng</Label>
          </div>
        </div>
      </div>
    </div>
  );
}

export default BottomBar;
