// TypeScript interfaces for Sales Order Status Report

export interface SaleOrderStatusItem {
  // System fields
  syspivot: string;
  sysorder: number;
  sysprint: string;
  systotal?: string;
  id: string;
  line: number;
  unit_id: string;

  // Document fields
  ngay_ct: string;
  so_ct: string;
  ma_kh: string;
  status: string;
  ma_ct: string;
  ma_vt: string;
  dvt: string;
  he_so: number;

  // Date and pricing fields
  ngay_giao: string;
  gia: number;
  tien: number;
  gia2?: number;
  tien2?: string | number;

  // Quantity fields
  so_luong: number;
  sl_nhap: number;
  sl_hd: number;
  sl_dh: number;
  sl_tl: number;
  sl_cl: number;
  sl_xuat?: number;

  // Name fields
  ten_kh: string;
  ten_vt: string;
  ten_ttct: string;
  ma_unit: string;
  ma_ngv?: string;
  ma_nvbh?: string;
  ten_nvbh?: string;
  ma_bp?: string;
  ma_cp0?: string;
  ma_dtt?: string;
  ma_hd?: string;
  ma_ku?: string;
  ma_lsx?: string | null;
  ma_phi?: string;
  ma_sp?: string;
  ma_vv?: string;
}

export interface SaleOrderStatusResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: SaleOrderStatusItem[];
  summary?: {
    total_records: number;
    total_so_luong: number;
    total_sl_nhap: number;
    total_sl_cl: number;
  };
  filters_applied?: {
    [key: string]: any;
  };
  entity_slug?: string;
}

export interface SearchFormValues {
  // Date range fields
  ngay_ct1: string;
  ngay_ct2: string;
  so_ct1: string;
  so_ct2: string;

  ma_kh?: string;
  nh_kh1?: string;
  nh_kh2?: string;
  nh_kh3?: string;

  // Material fields
  ma_vt?: string;
  ma_lvt?: string;
  nh_vt1?: string;
  nh_vt2?: string;
  nh_vt3?: string;

  // Location fields
  ma_kho?: string;
  ma_lo?: string;
  ma_vi_tri?: string;

  // Status and delivery fields
  ngay_giao: string;
  status: string;
  ma_ngv: string;

  // Report configuration
  mau_bc: number;

  // Other fields
  ma_gd: string;
  dien_giai: string;
  report_filtering: string;
  ma_unit: string;

  // Pagination fields
  page?: number;
  page_size?: number;

  // Dynamic fields
  [key: string]: any;
}

export interface UseSaleOrderStatusReturn {
  data: SaleOrderStatusItem[];
  isLoading: boolean;
  error: Error | null;
  totalItems: number;
  currentPage: number;
  pageSize: number;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
  postData: (searchParams: SearchFormValues) => Promise<void>;
  handlePageChange: (page: number) => Promise<void>;
}

// Export type aliases for backward compatibility
export type { SearchFormValues as SaleOrderSearchFormValues };
export type { SaleOrderStatusItem as SaleOrderItem };
export type { SaleOrderStatusResponse as SaleOrderResponse };
