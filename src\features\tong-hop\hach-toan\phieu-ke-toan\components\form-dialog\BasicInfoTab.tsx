import { CurrencyInput, DocumentNumberField, FormField } from '@/components/custom/arito';
import { FormFieldState, FormFieldActions } from '../../hooks';
import { Label } from '@/components/ui/label';
import { MA_CHUNG_TU } from '@/constants';
import { FormMode } from '@/types/form';
import { useNgoaiTe } from '@/hooks';

interface BasicInfoTabProps {
  formMode: FormMode;
  formState: {
    state: FormFieldState;
    actions: FormFieldActions;
  };
}

export const BasicInfoTab = ({ formMode, formState: { state, actions } }: BasicInfoTabProps) => {
  return (
    <div className='flex p-4'>
      {/* Tab Content */}
      <div className='grid w-full grid-cols-[3fr_1fr] gap-8'>
        <div>
          <DocumentNumberField
            ma_ct={MA_CHUNG_TU.TONG_HOP.PHIEU_KE_TOAN}
            quyenChungTu={state.quyenChungTu}
            onQuyenChungTuChange={actions.setQuyenChungTu}
            soChungTu={state.soChungTu || ''}
            onSoChungTuChange={actions.setSoChungTu}
            disabled={formMode === 'view'}
            labelClassName='w-32 shrink-0'
          />

          <FormField
            label='Ngày chứng từ'
            name='ngay_ct'
            type='date'
            disabled={formMode === 'view'}
            labelClassName='w-32 shrink-0'
            className='w-[325px]'
          />

          <FormField
            label='Diễn giải'
            name='dien_giai'
            type='text'
            disabled={formMode === 'view'}
            labelClassName='w-32 shrink-0'
            inputClassName='w-full'
          />
        </div>

        <div>
          <CurrencyInput formMode={formMode} labelClassName='w-[127px]' classNameInput='w-full' />

          <FormField
            label='Trạng thái'
            name='status'
            type='select'
            disabled={formMode === 'view'}
            labelClassName='w-32 shrink-0'
            inputClassName='w-60'
            options={[
              { value: '0', label: 'Chưa ghi sổ' },
              { value: '1', label: 'Chờ duyệt' },
              { value: '2', label: 'Đã ghi sổ' }
            ]}
          />
          <div className='mt-2 flex items-center'>
            <div className='w-10'></div>
            <FormField name='transfer_yn' type='checkbox' disabled={formMode === 'view'} className='ml-12' />
            <Label className='w-32 shrink-0'>Dữ liệu được nhận</Label>
          </div>
        </div>
        {/* Diễn giải field spans full width */}
      </div>
    </div>
  );
};
