import { z } from 'zod';

export const formSchema = z.object({
  ma_nt: z.string().optional(),
  ty_gia: z.coerce.number().optional(),

  ngay_ct: z.string().optional(),
  ngay_lct: z.string().optional(),
  dien_giai: z.string().optional(),

  transfer_yn: z.boolean().optional(),

  status: z.string().optional(),

  t_ps_no_nt: z.coerce.number().optional(),
  t_ps_co_nt: z.coerce.number().optional()
});

export type FormSchema = z.infer<typeof formSchema>;

export const initialFormValues: FormSchema = {
  ma_nt: '',
  ty_gia: 0,

  ngay_ct: new Date().toISOString().split('T')[0],
  ngay_lct: new Date().toISOString().split('T')[0],
  dien_giai: '',

  transfer_yn: false,

  status: '',

  t_ps_no_nt: 0,
  t_ps_co_nt: 0
};
