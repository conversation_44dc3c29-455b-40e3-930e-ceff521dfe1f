import React, { useState } from 'react';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import SaveTemplateDialog, { SaveTemplateFormData } from '../SaveTemplateDialog';
import { FormField } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';

export const OtherTab: React.FC = () => {
  const [saveTemplateDialogOpen, setSaveTemplateDialogOpen] = useState(false);
  const [currentTemplateType, setCurrentTemplateType] = useState<'filter' | 'analysis'>('filter');

  const handleOpenSaveDialog = (templateType: 'filter' | 'analysis') => {
    setCurrentTemplateType(templateType);
    setSaveTemplateDialogOpen(true);
  };

  const handleSaveTemplate = (data: SaveTemplateFormData) => {
    console.log(`Saving ${currentTemplateType} template:`, data);
    setSaveTemplateDialogOpen(false);
  };

  return (
    <div className='h-32 space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Mẫu lọc báo cáo:</Label>
          <div className='flex items-center gap-1'>
            <FormField
              name='report_filtering'
              type='select'
              options={[{ value: '0', label: 'Người dùng tự lọc' }]}
              className='w-96'
            />

            <div className='flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={624}
                items={[
                  {
                    value: 'save_new',
                    label: 'Lưu mẫu mới',
                    icon: 7,
                    onClick: () => handleOpenSaveDialog('filter')
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => console.log('Overwrite current filter template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current filter template')
                  }
                ]}
              />
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Mẫu phân tích DL:</Label>
          <div className='flex items-center gap-1'>
            <FormField
              name='data_analysis_struct'
              type='select'
              options={[{ value: '0', label: 'Không phân tích' }]}
              className='w-96'
            />

            <div className='flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={790}
                items={[
                  {
                    value: 'save_new',
                    label: 'Lưu mẫu mới',
                    icon: 7,
                    onClick: () => handleOpenSaveDialog('analysis')
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Sửa mẫu đang chọn',
                    icon: 9,
                    onClick: () => console.log('Overwrite current filter template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current filter template')
                  }
                ]}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Combined Save Template Dialog */}
      <SaveTemplateDialog
        open={saveTemplateDialogOpen}
        onClose={() => setSaveTemplateDialogOpen(false)}
        onSave={handleSaveTemplate}
        templateType={currentTemplateType}
      />
    </div>
  );
};
