'use client';

import { useState, useEffect, useMemo } from 'react';
import { getFormTitle, getFormActionButtons, calculateTotals, transformFormData } from '../../utils';
import { AritoHeaderTabs, AritoForm } from '@/components/custom/arito';
import { handleUpdateRowFields } from '../../utils/calc-util';
import { FormSchema, initialValues } from '../../schema';
import { useDetailRows, useTaxRows } from './hooks';
import { useAuth } from '@/contexts/auth-context';
import { PaymentInfoTab } from './PaymentInfoTab';
import { ConfirmDialog } from '../../components';
import { useFormFieldState } from '../../hooks';
import { BasicInfoTab } from './BasicInfoTab';
import { HistoryTab } from './HistoryTab';
import { FormMode } from '@/types/form';
import { BottomBar } from './BottomBar';
import { DetailTab } from './DetailTab';
import { OtherTab } from './OtherTab';
import { RateTab } from './RateTab';
import { TaxTab } from './TaxTab';

interface FormDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: any;
  onSubmit?: (data: any) => Promise<void> | void;
  onClose: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
}

const FormDialog = ({
  open,
  formMode,
  initialData,
  onClose,
  onSubmit,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}: FormDialogProps) => {
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('info');
  const { entityUnit } = useAuth();

  const {
    rows: detailRows,
    selectedRowUuid: detailSelectedRowUuid,
    handleRowClick: detailHandleRowClick,
    handleAddRow: detailHandleAddRow,
    handleDeleteRow: detailHandleDeleteRow,
    handleCopyRow: detailHandleCopyRow,
    handlePasteRow: detailHandlePasteRow,
    handleMoveRow: detailHandleMoveRow,
    handleCellValueChange: detailHandleCellValueChange
  } = useDetailRows(initialData?.chi_tiet || [], undefined, handleUpdateRowFields);
  const {
    rows: taxRows,
    selectedRowUuid: taxSelectedRowUuid,
    handleRowClick: taxHandleRowClick,
    handleAddRow: taxHandleAddRow,
    handleDeleteRow: taxHandleDeleteRow,
    handleCopyRow: taxHandleCopyRow,
    handlePasteRow: taxHandlePasteRow,
    handleMoveRow: taxHandleMoveRow,
    handleCellValueChange: taxHandleCellValueChange
  } = useTaxRows(initialData?.thue || []);
  const { state, actions } = useFormFieldState(initialData);

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
      setActiveTab('info');
    }
  }, [open]);

  const detail = useMemo(() => {
    return initialData?.chi_tiet || detailRows;
  }, [initialData, detailRows]);

  const { tongTien, tongThue, tongThanhToan } = useMemo(() => {
    return calculateTotals(detail, taxRows);
  }, [detail, taxRows]);

  const handleSubmit = async (data: any) => {
    const formData = transformFormData(data, state, formMode, tongTien, tongThue, tongThanhToan, detailRows, taxRows);

    if (formMode === 'edit') {
      onSubmit?.({ ...formData, unit_id: entityUnit?.uuid, i_so_ct: initialData?.i_so_ct, ma_nk: initialData?.ma_nk });
    } else {
      onSubmit?.({ ...formData, unit_id: entityUnit?.uuid, ngay_lct: new Date().toISOString().split('T')[0] });
    }
    setIsFormDirty(false);
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const title = getFormTitle(formMode, activeTab);
  const actionButtons = getFormActionButtons(formMode, activeTab, {
    onAdd,
    onEdit,
    onDelete,
    onCopy,
    handleClose
  });

  if (initialData && formMode === 'add') {
    initialData = {
      ...initialData,
      ngay_ct: new Date().toISOString().split('T')[0],
      ngay_lct: new Date().toISOString().split('T')[0]
    };
  }
  return (
    <>
      <AritoForm
        mode={formMode}
        initialData={initialData || initialValues}
        title={title}
        actionButtons={actionButtons}
        schema={FormSchema}
        subTitle='Phiếu chi tiền'
        onSubmit={handleSubmit}
        onClose={handleClose}
        headerFields={
          <div onChange={() => setIsFormDirty(true)}>
            <AritoHeaderTabs
              tabs={[
                {
                  id: 'info',
                  label: 'Thông tin',
                  component: <BasicInfoTab formMode={formMode} formState={{ state, actions }} />
                },
                ...(formMode === 'view'
                  ? [
                      {
                        id: 'history',
                        label: 'Lịch sử',
                        component: <HistoryTab />
                      }
                    ]
                  : [])
              ]}
              onTabChange={handleTabChange}
              defaultTabIndex={activeTab === 'info' ? 0 : activeTab === 'history' ? 1 : 0}
            />
          </div>
        }
        tabs={
          activeTab === 'info' && [
            {
              id: 'details',
              label: 'Chi tiết',
              component: (
                <DetailTab
                  formMode={formMode}
                  rows={detailRows}
                  selectedRowUuid={detailSelectedRowUuid}
                  onRowClick={detailHandleRowClick}
                  onAddRow={detailHandleAddRow}
                  onDeleteRow={detailHandleDeleteRow}
                  onCopyRow={detailHandleCopyRow}
                  onPasteRow={detailHandlePasteRow}
                  onMoveRow={detailHandleMoveRow}
                  onCellValueChange={detailHandleCellValueChange}
                />
              )
            },
            ...(state.loaiPhieuChi !== '4'
              ? [
                  {
                    id: 'tax',
                    label: 'Thuế',
                    component: (
                      <TaxTab
                        formMode={formMode}
                        rows={taxRows}
                        selectedRowUuid={taxSelectedRowUuid}
                        onRowClick={taxHandleRowClick}
                        onAddRow={taxHandleAddRow}
                        onDeleteRow={taxHandleDeleteRow}
                        onCopyRow={taxHandleCopyRow}
                        onPasteRow={taxHandlePasteRow}
                        onMoveRow={taxHandleMoveRow}
                        onCellValueChange={taxHandleCellValueChange}
                      />
                    )
                  },
                  {
                    id: 'payment',
                    label: 'Thanh toán',
                    component: <PaymentInfoTab formMode={formMode} formState={{ state, actions }} />
                  }
                ]
              : []),
            {
              id: 'rate',
              label: 'Tỷ giá',
              component: <RateTab formMode={formMode} />
            },
            {
              id: 'other',
              label: 'Khác',
              component: <OtherTab formMode={formMode} />
            }
          ]
        }
        bottomBar={
          activeTab === 'info' && <BottomBar tongTien={tongTien} tongThue={tongThue} tongThanhToan={tongThanhToan} />
        }
      />

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default FormDialog;
