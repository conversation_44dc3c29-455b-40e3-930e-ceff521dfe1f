import { transformDocumentNumber } from '@/components/custom/arito/form/document-number/util';
import { isValidUUID } from '@/lib/uuid-validator';
import { MA_CHUNG_TU } from '@/constants';
import { FormFieldState } from '../hooks';

/**
 * Transform detail rows for API submission
 * @param detailRows - Array of detail row data from the form
 * @returns Transformed detail rows ready for API submission
 */
export const transformDetailRows = (detailRows: any[], formMode: string) => {
  return detailRows.map((row: any, index: number) => {
    const uuid = formMode !== 'add' || isValidUUID(row.uuid) ? { uuid: row.uuid } : {};
    return {
      line: index + 1,
      ...uuid,

      // Description and basic info
      dien_giai: row.dien_giai || null,
      ghi_chu: row.ghi_chu || null,

      // Customer information
      ma_kh: row.ma_kh_data?.uuid || row.ma_kh || null,

      // Account information
      tk_no: row.tk_no_data?.uuid || row.tk_no || null,
      tknh2: row.tknh2 || null,

      // Document references
      so_ct0: row.so_ct0_data?.uuid || row.so_ct0 || null,
      so_ct2: row.so_ct2_data?.uuid || row.so_ct2 || null,
      ngay_ct0: row.ngay_ct0 || null,

      // Financial amounts
      ty_gia2: row.ty_gia2 || 0,
      tien_nt: row.tien_nt || 0,
      tien: row.tien || 0,

      // Tax information
      ma_thue: row.ma_thue_data?.uuid || null,
      thue_suat: row.thue_suat || 0,
      tk_thue: row.tk_thue || null,
      thue_nt: row.thue_nt || 0,
      thue: row.thue_nt || 0,

      // Invoice and contract information
      ma_loai_hd: row.ma_loai_hd || null,
      id_hd: row.id_hd_data?.ID || null,
      ma_mau_ct: row.ma_mau_ct || null,
      ma_mau_bc: row.ma_mau_bc || null,
      ma_tc_thue: row.ma_tc_thue_data?.uuid || null,
      ma_kh_thue: row.ma_kh_thue_data?.uuid || null,
      dia_chi: row.ma_kh_thue_data?.address || row.dia_chi || null,
      ma_so_thue: row.ma_kh_thue_data?.tax_code || row.ma_so_thue || null,
      ten_vt_thue: row.ten_vt_thue || null,

      // Additional customer info
      ma_kh9: row.ma_kh9_data?.uuid || null,

      // Request and equipment tracking
      id_dn: row.id_dn || 0,
      line_dn: row.line_dn || 0,
      id_tb: row.id_tb || 0,
      line_tb: row.line_tb || 0,
      id_tt: row.id_tt || 0,

      // Department and organizational units
      ma_bp: row.ma_bp_data?.uuid || row.ma_bp || null,
      ma_vv: row.ma_vv_data?.uuid || row.ma_vv || null,
      ma_hd: row.ma_hd_data?.uuid || row.ma_hd || null,
      ma_dtt: row.ma_dtt_data?.uuid || row.ma_dtt || null,
      ma_ku: row.ma_ku_data?.uuid || row.ma_ku || null,
      ma_phi: row.ma_phi_data?.uuid || row.ma_phi || null,
      ma_sp: row.ma_sp_data?.uuid || row.ma_sp || null,
      ma_lsx: row.ma_lsx || null,
      ma_cp0: row.ma_cp0_data?.uuid || row.ma_cp0 || null
    };
  });
};

export const transformTaxRows = (taxRows: any[], formMode: string) => {
  return taxRows.map((row: any, index: number) => {
    const uuid = formMode !== 'add' || isValidUUID(row.uuid) ? { uuid: row.uuid } : {};

    return {
      ...uuid,
      line: index + 1,
      so_ct0: row.so_hd || null,
      so_ct2: row.so_ct2 || null,
      ngay_ct0: row.ngay_hd || null,
      ma_thue: row.ma_thue || null,
      ma_mau_ct: row.ma_mau_ct || null,
      ma_mau_bc: row.ma_mau_bc || null,
      ma_tc_thue: row.ma_tc_thue || null,
      ma_kh: row.ma_ncc_data?.uuid || null,
      ten_kh_thue: row.ma_ncc_data?.customer_name || null,
      dia_chi: row.ma_ncc_data?.address || null,
      ma_so_thue: row.ma_ncc_data?.tax_code || null,
      ten_vt_thue: row.ten_vt_thue || null,
      t_tien_nt: row.t_tien_nt || 0,
      tk_thue_no: row.tk_thue_data?.uuid || null,
      tk_du: row.tk_doi_ung_data?.uuid || null,
      t_thue_nt: row.t_thue_nt || 0,
      ma_kh9: row.cuc_thue_data?.uuid || null,
      ma_tt: row.ma_tt_data?.uuid || null,
      ghi_chu: row.ghi_chu || null,
      ma_bp: row.ma_bp_data?.uuid || null,
      ma_vv: row.ma_vv_data?.uuid || null,
      ma_hd: row.ma_hd_data?.uuid || null,
      ma_dtt: row.ma_dtt_data?.uuid || null,
      ma_ku: row.ma_ku_data?.uuid || null,
      ma_phi: row.ma_phi_data?.uuid || null,
      ma_sp: row.ma_sp_data?.uuid || null,
      ma_lsx: row.ma_lsx_data?.uuid || null,
      ma_cp0: row.ma_cp0_data?.uuid || null
    };
  });
};

export const transformFormData = (
  data: any,
  state: FormFieldState,
  formMode: string,
  tongTien: number,
  tongThue: number,
  tongThanhToan: number,
  detailRows: any[] = [],
  taxRows: any[] = []
) => {
  const chi_tiet = transformDetailRows(detailRows, formMode);
  const thue = transformTaxRows(taxRows, formMode);

  return {
    ...data,

    // Document type and basic info
    ma_ngv: state.loaiPhieuChi || '1',

    // Account information from state
    tk: state.taiKhoan?.uuid || null,

    // Document number and series from state
    ...transformDocumentNumber(state.quyenChungTu, state.soChungTu),

    ma_tt: state.thanhToan?.uuid || null,

    // Total amounts
    t_tien_nt: tongTien,
    t_tien: tongTien,
    t_thue_nt: tongThue,
    t_thue: tongThue,
    t_tt_nt: tongThanhToan,
    t_tt: tongThanhToan,

    chi_tiet,
    thue
  };
};
