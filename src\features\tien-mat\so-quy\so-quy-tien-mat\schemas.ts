import { z } from 'zod';

export const searchSchema = z.object({
  // Basic fields
  ngay_ct1: z.string().nonempty('<PERSON>ui lòng chọn'),
  ngay_ct2: z.string().nonempty('Vui lòng chọn'),
  ngay_ms: z.string().optional(),

  // Detail fields
  phan_loai: z.coerce.number().optional(),
  mau_bc: z.coerce.number().optional(),

  // Other fields
  report_filtering: z.string().optional(),
  data_analysis_struct: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  ngay_ct1: '',
  ngay_ct2: '',
  phan_loai: 1,
  mau_bc: 20,
  report_filtering: '0',
  data_analysis_struct: '0'
};

// Legacy exports for compatibility
export const cashBookFilterSchema = searchSchema;
export const cashBookFilterInitialValues = initialValues;
