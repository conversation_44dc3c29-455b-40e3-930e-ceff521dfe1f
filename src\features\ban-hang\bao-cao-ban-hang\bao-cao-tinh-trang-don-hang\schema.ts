import { z } from 'zod';

export const searchSchema = z.object({
  ngay_ct1: z.string(),
  ngay_ct2: z.string(),
  ngay_giao: z.string(),
  so_ct1: z.string(),
  so_ct2: z.string(),
  status: z.string(),
  ma_ngv: z.string(),
  ma_gd: z.string(),
  dien_giai: z.string(),
  report_filtering: z.string(),
  mau_bc: z.number(),
  ma_unit: z.string(),
  page: z.number().optional(),
  page_size: z.number().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialSearchValues: SearchFormValues = {
  ngay_ct1: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
  ngay_ct2: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).toISOString().split('T')[0],
  ngay_giao: new Date().toISOString().split('T')[0],
  so_ct1: '',
  so_ct2: '',
  status: '__all__',
  ma_ngv: '__all__',
  ma_gd: '',
  dien_giai: '',
  report_filtering: '__all__',
  mau_bc: 20,
  ma_unit: '',
  page: 1,
  page_size: 20
};

// Transform function to convert UI values to API values
export const transformSearchValuesForAPI = (values: SearchFormValues): SearchFormValues => {
  return {
    ...values,
    status: values.status === '__all__' ? '' : values.status,
    ma_ngv: values.ma_ngv === '__all__' ? '' : values.ma_ngv,
    report_filtering: values.report_filtering === '__all__' ? '' : values.report_filtering
  };
};
