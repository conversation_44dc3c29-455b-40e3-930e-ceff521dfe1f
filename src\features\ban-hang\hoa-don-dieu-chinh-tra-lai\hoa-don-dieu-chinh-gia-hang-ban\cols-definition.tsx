import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { format } from 'date-fns';
import { CellField } from '@/components/custom/arito/custom-input-table/components';

export const getSupplierReturnReceiptColumns = (handleOpenViewForm: () => void): GridColDef[] => [
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      switch (params.row.status) {
        case '0':
          return 'Chưa ghi sổ';
        case '3':
          return 'Chờ duyệt';
        case '5':
          return 'Đã ghi sổ';
        case '9':
          return 'Hủy';
      }
    }
  },
  {
    field: 'ma_tthddt',
    headerName: 'Trạng thái HĐĐT',
    width: 150,
    renderCell: (params: GridRenderCellParams) => {
      switch (params.row.ma_tthddt) {
        case '0':
          return 'Không sử dụng';
        case '1':
          return 'Chờ phát hành';
        case '2':
          return 'Chờ ký';
        case '3':
          return 'Xác thực';
        case '4':
          return 'Điều chỉnh';
        case '5':
          return 'Thay thế';
        case '7':
          return 'Thuế từ chối';
        case '8':
          return 'Chờ hủy';
        case '9':
          return 'Hủy';
        case '10':
          return 'Thuế không đồng ý';
      }
    }
  },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 150,
    renderCell: (params: GridRenderCellParams) => (
      <div className='cursor-pointer text-teal-600 hover:underline' onClick={handleOpenViewForm}>
        {params.row.so_ct}
      </div>
    )
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày c/từ',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return format(params.row.ngay_ct, 'dd/MM/yyyy');
    }
  },
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 150,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.ma_kh_data?.customer_code;
    }
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 200,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.ma_kh_data?.customer_name;
    }
  },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 200 },
  {
    field: 'tk',
    headerName: 'Tài khoản',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.tk_data?.code;
    }
  },
  {
    field: 't_tt_nt',
    headerName: 'Tổng tiền',
    width: 180,
    renderCell: (params: GridRenderCellParams) => <CellField name='t_tt_nt' type='number' value={params.row.t_tt_nt} />
  },
  {
    field: 'ma_nt',
    headerName: 'Ngoại tệ',
    width: 100,
    renderCell: (params: GridRenderCellParams) => {
      return <div className='text-center'>{params.row.ma_nt_data?.ma_nt}</div>;
    }
  }
];

export const getSupplierReturnReceiptDetailColumns: GridColDef[] = [
  {
    field: 'ma_vt',
    headerName: 'Mã sản phẩm',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.ma_vt_data?.ma_vt;
    }
  },
  {
    field: 'ten_vt',
    headerName: 'Tên sản phẩm',
    width: 200,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.ma_vt_data?.ten_vt;
    }
  },
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 80,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.ma_vt_data?.dvt_data?.dvt;
    }
  },
  {
    field: 'ma_kho',
    headerName: 'Mã kho',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.ma_kho_data?.ma_kho;
    }
  },
  {
    field: 'ma_vi_tri',
    headerName: 'Mã vị trí',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.ma_vi_tri_data?.ma_vi_tri;
    }
  },
  {
    field: 'so_luong',
    headerName: 'Số lượng',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <CellField name='so_luong' type='number' value={params.row.so_luong} />
    )
  },
  { field: 'ct_km', headerName: 'Loại hàng', width: 100 },
  { field: 'pn_tb', headerName: 'Trung bình', width: 100 },
  {
    field: 'gia_nt',
    headerName: 'Giá tồn %s',
    width: 120,
    renderCell: (params: GridRenderCellParams) => <CellField name='gia_nt' type='number' value={params.row.gia_nt} />
  },
  {
    field: 'tien_nt',
    headerName: 'Tiền %s',
    width: 120,
    renderCell: (params: GridRenderCellParams) => <CellField name='tien_nt' type='number' value={params.row.tien_nt} />
  },
  {
    field: 'gia_nt1',
    headerName: 'Giá chuẩn %s',
    width: 120,
    renderCell: (params: GridRenderCellParams) => <CellField name='gia_nt1' type='number' value={params.row.gia_nt1} />
  },
  {
    field: 'gia_nt2',
    headerName: 'Giá bán %s',
    width: 120,
    renderCell: (params: GridRenderCellParams) => <CellField name='gia_nt2' type='number' value={params.row.gia_nt2} />
  },
  {
    field: 'tien_nt2',
    headerName: 'Doanh số %s',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <CellField name='tien_nt2' type='number' value={params.row.tien_nt2} />
    )
  },
  { field: 'tl_ck', headerName: 'Tl ck(%)', width: 100, type: 'number' },
  {
    field: 'ck_nt',
    headerName: 'Ch.khấu %s',
    width: 120,
    renderCell: (params: GridRenderCellParams) => <CellField name='ck_nt' type='number' value={params.row.ck_nt} />
  },
  { field: 'ma_thue', headerName: 'Thuế suất', width: 100 },
  { field: 'tk_thue_no', headerName: 'Tk thuế nợ', width: 120 },
  { field: 'thue_suat', headerName: 'Thuế suất(%)', width: 120, type: 'number' },
  {
    field: 'thue_nt',
    headerName: 'Thuế %s',
    width: 120,
    renderCell: (params: GridRenderCellParams) => <CellField name='thue_nt' type='number' value={params.row.thue_nt} />
  },
  {
    field: 'tk_dt',
    headerName: 'Tk trả lại',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.tk_dt_data?.code;
    }
  },
  {
    field: 'tk_gv',
    headerName: 'Tk giá vốn',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.tk_gv_data?.code;
    }
  },
  {
    field: 'tk_vt',
    headerName: 'Tk kho',
    width: 100,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.tk_vt_data?.code;
    }
  },
  {
    field: 'tk_ck',
    headerName: 'Tk chiết khấu',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.tk_ck_data?.code;
    }
  },
  {
    field: 'tk_km',
    headerName: 'Tk khuyến mãi',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.tk_km_data?.code;
    }
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 100,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.ma_bp_data?.ma_bp;
    }
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 100,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.ma_vv_data?.ma_vu_viec;
    }
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 100,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.ma_hd_data?.ma_hd;
    }
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 150,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.ma_dtt_data?.ma_dtt;
    }
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 100,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.ma_ku_data?.ma_ku;
    }
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 100,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.ma_phi_data?.ma_phi;
    }
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 100,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.ma_sp_data?.ma_vt;
    }
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 150,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.ma_lsx_data?.ma_lsx;
    }
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 150,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.ma_cp0_data?.ma_cp0;
    }
  },
  { field: 'gia', headerName: 'Giá tồn', width: 100, type: 'number' },
  { field: 'tien', headerName: 'Tiền', width: 100, type: 'number' },
  { field: 'gia1', headerName: 'Giá chuẩn', width: 100, type: 'number' },
  { field: 'gia2', headerName: 'Giá bán', width: 100, type: 'number' },
  { field: 'tien2', headerName: 'Doanh số', width: 100, type: 'number' },
  { field: 'ck', headerName: 'Chiết khấu', width: 100, type: 'number' },
  { field: 'thue', headerName: 'Thuế', width: 100, type: 'number' },
  { field: 'so_ct_px', headerName: 'Số PX', width: 100 },
  { field: 'line_px', headerName: 'Dòng px', width: 100 },
  { field: 'so_ct_hd', headerName: 'Số hóa đơn', width: 120 },
  { field: 'line_hd', headerName: 'Dòng HĐ', width: 100 }
];

export const historyColumns: GridColDef[] = [
  { field: 'username', headerName: 'Tài khoản', width: 100 },
  { field: 'nickname', headerName: 'Tên tài khoản', width: 200 },
  { field: 'action', headerName: 'Hành động', width: 100 },
  { field: 'datetime0', headerName: 'Thời gian', width: 150 }
];
