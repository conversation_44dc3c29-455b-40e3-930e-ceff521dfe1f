import { GridRowParams } from '@mui/x-data-grid';
import { useState, useEffect } from 'react';
import { useSaleOrderStatusData } from './useSalesOrderStatusData';
import { SearchFormValues, initialSearchValues } from '../schema';
import { orderStatusReportColumns } from '../cols-definition';

export interface UseTableDataReturn {
  tables: Array<{
    name: string;
    rows: any[];
    columns: any[];
  }>;
  handleRowClick: (params: GridRowParams) => void;
  isLoading: boolean;
  error: Error | null;
  refreshData: () => Promise<void>;
  totalItems: number;
  currentPage: number;
  handlePageChange: (page: number) => Promise<void>;
}

export function useTableData(searchParams: SearchFormValues = initialSearchValues): UseTableDataReturn {
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);

  const { data, isLoading, error, refreshData, totalItems, currentPage, handlePageChange } =
    useSaleOrderStatusData(searchParams);

  const handleRowClick = (params: GridRowParams) => {
    const order = params.row as any;
    setSelectedRowIndex(params.row.id.toString());
    console.log('Row clicked:', order);
  };

  const tables = [
    {
      name: 'Báo cáo tình trạng đơn hàng mua',
      rows: data,
      columns: orderStatusReportColumns(
        () => {},
        () => {}
      )
    }
  ];

  return {
    tables,
    handleRowClick,
    isLoading,
    error,
    refreshData,
    totalItems,
    currentPage,
    handlePageChange
  };
}
