import { GridRowParams } from '@mui/x-data-grid';
import { useState, useEffect } from 'react';
import { useBaoCaoSoDuTaiQuyCuaNganHang } from './useBaoCaoSoDuTaiQuyCuaNganHang';
import type { BaoCaoSoDuTaiQuyCuaNganHangItem } from '../schema';
import { exportAccountColumns } from '../cols-definition';

export function useTableData(searchParams: any) {
  const [selectedObj, setSelectedObj] = useState<BaoCaoSoDuTaiQuyCuaNganHangItem | null>(null);
  const { data, isLoading, error, fetchData, refreshData } = useBaoCaoSoDuTaiQuyCuaNganHang(searchParams);

  // Fetch data when searchParams change
  useEffect(() => {
    if (searchParams && Object.keys(searchParams).length > 0) {
      fetchData(searchParams);
    }
  }, [searchParams, fetchData]);

  const handleRowClick = (params: GridRowParams) => {
    setSelectedObj(params.row as BaoCaoSoDuTaiQuyCuaNganHangItem);
  };

  const handleOpenViewForm = (obj: BaoCaoSoDuTaiQuyCuaNganHangItem) => {
    console.log('Open view form:', obj);
  };

  const handleOpenEditForm = (obj: BaoCaoSoDuTaiQuyCuaNganHangItem) => {
    console.log('Open edit form:', obj);
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: data.filter(row => row.disabled === '1'),
      columns: exportAccountColumns(handleOpenViewForm, handleOpenEditForm),
      tabProps: { className: 'whitespace-nowrap' }
    }
  ];

  return {
    tables,
    selectedObj,
    isLoading,
    error,
    handleRowClick,
    refreshData
  };
}
