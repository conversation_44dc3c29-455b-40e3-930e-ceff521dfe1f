import { GridCellParams } from '@mui/x-data-grid';
import { useState, useCallback } from 'react';

export interface DetailRow {
  uuid?: string | null;
  dien_giai?: string;
  [key: string]: any;
}

export interface SelectedCellInfo {
  id: string;
  field: string;
}

export function useDetailRows(initialRows: DetailRow[] = [], initialDienGiai?: string) {
  const [rows, setRows] = useState<DetailRow[]>(initialRows);
  const [selectedRowUuid, setSelectedRowUuid] = useState<string | null>(null);
  const [selectedRow, setSelectedRow] = useState<DetailRow | null>(null);
  const [selectedCell, setSelectedCell] = useState<SelectedCellInfo | null>(null);

  const updateDienGiaiForEmptyRows = useCallback((newDienGiai: string) => {
    setRows(prevRows => {
      let hasChanges = false;
      const updatedRows = prevRows.map(row => {
        if ((!row.dien_giai || row.dien_giai.trim() === '') && newDienGiai !== row.dien_giai) {
          hasChanges = true;
          return { ...row, dien_giai: newDienGiai };
        }
        return row;
      });

      return hasChanges ? updatedRows : prevRows;
    });
  }, []);

  const handleAddRow = () => {
    const newRow = {
      uuid: String(Math.random()),
      dien_giai: initialDienGiai || ''
    };

    setRows([...rows, newRow]);

    // Select the newly added row
    setSelectedRowUuid(newRow.uuid);
    setSelectedRow(newRow);
  };

  const handleDeleteRow = () => {
    let updatedRows: DetailRow[] = [];

    if (selectedRowUuid) {
      updatedRows = rows.filter(row => row.uuid !== selectedRowUuid);
      setRows(updatedRows);
    } else if (rows.length > 0) {
      updatedRows = rows.slice(0, -1);
      setRows(updatedRows);
    }

    // If there are remaining rows, select the last one
    if (updatedRows.length > 0) {
      const lastRow = updatedRows[updatedRows.length - 1];
      setSelectedRowUuid(lastRow.uuid || null);
      setSelectedRow(lastRow);
    } else {
      // If no rows remain, clear selection
      clearSelection();
    }
  };

  const handleCopyRow = () => {
    if (!selectedRow) return;

    const { uuid, ...rowDataWithoutUuid } = selectedRow;

    const newRow = {
      ...rowDataWithoutUuid,
      uuid: String(Math.random())
    };

    setRows([...rows, newRow]);

    // Select the newly copied row
    setSelectedRowUuid(newRow.uuid);
    setSelectedRow(newRow);
  };

  const handlePasteRow = () => {
    if (!selectedRow) return;

    const { uuid, ...rowDataWithoutUuid } = selectedRow;

    let newRow = {
      ...rowDataWithoutUuid,
      uuid: String(Math.random())
    };

    if (selectedRowUuid) {
      const selectedIndex = rows.findIndex(row => row.uuid === selectedRowUuid);
      if (selectedIndex === -1) return;

      const newRows = [...rows];
      newRows.splice(selectedIndex + 1, 0, newRow);

      setRows(newRows);
    } else {
      setRows([...rows, newRow]);
    }

    // Select the newly pasted row
    setSelectedRowUuid(newRow.uuid);
    setSelectedRow(newRow);
  };

  const handleMoveRow = (direction: 'up' | 'down') => {
    if (!selectedRowUuid) return;

    const currentIndex = rows.findIndex(row => row.uuid === selectedRowUuid);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? Math.max(0, currentIndex - 1) : Math.min(rows.length - 1, currentIndex + 1);

    if (newIndex === currentIndex) return;

    const newRows = [...rows];
    const [movedRow] = newRows.splice(currentIndex, 1);
    newRows.splice(newIndex, 0, movedRow);

    setRows(newRows);

    // Ensure selection follows the moved row
    // No need to update selectedRowUuid as it remains the same
    setSelectedRow(movedRow);
  };

  const handleCellValueChange = (rowUuid: string, field: string, newValue: any) => {
    const rowIndex = rows.findIndex(row => row.uuid === rowUuid);
    if (rowIndex === -1) return;

    const updatedRows = [...rows];
    updatedRows[rowIndex] = {
      ...updatedRows[rowIndex],
      [field]: newValue
    };

    setRows(updatedRows);

    // If the changed row is the currently selected row, update selectedRow
    if (rowUuid === selectedRowUuid) {
      setSelectedRow(updatedRows[rowIndex]);
    }
  };

  const handleRowClick = (params: { id: string; row: DetailRow }) => {
    const rowUuid = params.id || params.row?.uuid;
    if (!rowUuid) return;

    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row);
  };

  const handleCellClick = (params: GridCellParams) => {
    const rowUuid = params.id.toString();
    if (!rowUuid) return;

    // Update row selection
    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row as DetailRow);

    // Update cell selection
    setSelectedCell({
      id: rowUuid,
      field: params.field
    });
  };

  const clearSelection = () => {
    setSelectedRowUuid(null);
    setSelectedRow(null);
    setSelectedCell(null);
  };

  return {
    rows,
    setRows,
    selectedRowUuid,
    selectedRow,
    selectedCell,
    handleRowClick,
    handleCellClick,
    clearSelection,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange,
    updateDienGiaiForEmptyRows
  };
}
