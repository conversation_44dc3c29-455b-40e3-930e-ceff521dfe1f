import React, { useState } from 'react';

import { AritoForm, AritoHeaderTabs, AritoIcon, BottomBar } from '@/components/custom/arito';
import { searchSchema, initialValues, SearchFormValues } from '../schemas';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { DetailTab, OtherTab } from './tabs';
import BasicInfo from './tabs/BasicInfoTab';
import { TaiKhoan } from '@/types/schemas';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: SearchFormValues) => void;
}

const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const [taiKhoan, setTaiKhoan] = useState<TaiKhoan>();

  const handleSubmit = (data: any) => {
    onSearch({
      ...data,
      tk: taiKhoan || null
    });
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Sổ quỹ tiền mặt'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={data => {
          handleSubmit(data);
        }}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
            <BasicInfo setTaiKhoan={setTaiKhoan} />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailTab />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 px-2'
        bottomBar={<BottomBar onClose={onClose} mode='search' />}
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
