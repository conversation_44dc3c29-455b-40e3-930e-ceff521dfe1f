import { useState, useCallback, useEffect } from 'react';
import { useBaoCaoTinhTrangDonHangBan } from '@/hooks/queries'; // import đúng path của bạn

import { SaleOrderStatusItem, SaleOrderStatusResponse, SearchFormValues, UseSaleOrderStatusReturn } from '../types';
import api from '@/lib/api';

// Helper function to create summary row from API summary or calculate from data
const createSummaryRow = (data: SaleOrderStatusItem[], apiSummary?: any): SaleOrderStatusItem => {
  let summary;

  if (apiSummary) {
    // Use API provided summary if available
    summary = {
      gia2: apiSummary.total_gia2 || 0,
      tien2: apiSummary.total_tien2 || 0,
      so_luong: apiSummary.total_so_luong || 0,
      sl_dh: apiSummary.total_sl_dh || 0,
      sl_xuat: apiSummary.total_sl_xuat || 0,
      sl_hd: apiSummary.total_sl_hd || 0,
      sl_tl: apiSummary.total_sl_tl || 0,
      sl_cl: apiSummary.total_sl_cl || 0
    };
  } else {
    // Calculate from current page data as fallback
    summary = data.reduce(
      (acc, item) => {
        acc.gia2 += parseFloat(item.gia2?.toString() || '0');
        acc.tien2 += parseFloat(item.tien2?.toString() || '0');
        acc.so_luong += parseFloat(item.so_luong?.toString() || '0');
        acc.sl_dh += parseFloat(item.sl_dh?.toString() || '0');
        acc.sl_xuat += parseFloat(item.sl_xuat?.toString() || '0');
        acc.sl_hd += parseFloat(item.sl_hd?.toString() || '0');
        acc.sl_tl += parseFloat(item.sl_tl?.toString() || '0');
        acc.sl_cl += parseFloat(item.sl_cl?.toString() || '0');
        return acc;
      },
      {
        gia2: 0,
        tien2: 0,
        so_luong: 0,
        sl_dh: 0,
        sl_xuat: 0,
        sl_hd: 0,
        sl_tl: 0,
        sl_cl: 0
      }
    );
  }

  return {
    id: 'SUMMARY_ROW',
    systotal: 'TOTAL',
    line: 0,
    unit_id: '',
    ma_unit: '',
    ma_ngv: '',
    ngay_ct: '',
    so_ct: '',
    ma_kh: '',
    ma_nvbh: '',
    status: '',
    ma_ct: '',
    ma_vt: '',
    dvt: '',
    he_so: null as any, // Để trống thay vì hiển thị 0
    ten_vt: 'TỔNG CỘNG',
    ten_kh: '',
    ten_ttct: '',
    ten_nvbh: '',
    ngay_giao: '',
    gia: summary.gia2,
    tien: summary.tien2,
    gia2: summary.gia2,
    tien2: typeof summary.tien2 === 'number' ? summary.tien2.toFixed(2) : summary.tien2,
    so_luong: summary.so_luong,
    sl_nhap: 0,
    sl_hd: summary.sl_hd,
    sl_dh: summary.sl_dh,
    sl_tl: summary.sl_tl,
    sl_cl: summary.sl_cl,
    sl_xuat: summary.sl_xuat,
    ma_bp: '',
    ma_cp0: '',
    ma_dtt: '',
    ma_hd: '',
    ma_ku: '',
    ma_lsx: null,
    ma_phi: '',
    ma_sp: '',
    ma_vv: '',
    syspivot: '',
    sysorder: 0,
    sysprint: ''
  } as SaleOrderStatusItem;
};

/**
 * Custom hook for managing Sales Order Status Report data
 *
 * This hook provides functionality to fetch sales order status data
 * with server-side pagination support for handling large datasets.
 */
export function useSaleOrderStatusData(searchParams: SearchFormValues): UseSaleOrderStatusReturn {
  const [data, setData] = useState<SaleOrderStatusItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [pageSize] = useState<number>(20); // Default page size
  const { submitBaoCaoTinhTrangDonHangBan } = useBaoCaoTinhTrangDonHangBan();

  const fetchData = useCallback(
    async (searchParams: SearchFormValues) => {
      setIsLoading(true);
      setError(null);

      try {
        // Add pagination parameters to search params
        const paginatedParams = {
          ...searchParams,
          page: currentPage + 1, // API expects 1-based page numbers
          page_size: pageSize
        };

        const response = await submitBaoCaoTinhTrangDonHangBan(paginatedParams);
        const rawData = response.results || [];

        // Only add summary row on first page
        if (currentPage === 0) {
          // Create summary row and add it to the beginning
          const summaryRow = createSummaryRow(rawData, response.summary);
          const dataWithSummary = [summaryRow, ...rawData];
          setData(dataWithSummary);
        } else {
          setData(rawData);
        }

        setTotalItems(response.count || 0);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
        setError(new Error(errorMessage));
        setData([]);
        setTotalItems(0);
      } finally {
        setIsLoading(false);
      }
    },
    [submitBaoCaoTinhTrangDonHangBan, currentPage, pageSize]
  );

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  const postData = useCallback(
    async (searchParams: SearchFormValues) => {
      setIsLoading(true);
      setError(null);

      try {
        // Add pagination parameters to search params
        const paginatedParams = {
          ...searchParams,
          page: currentPage + 1, // API expects 1-based page numbers
          page_size: pageSize
        };

        const response = await api.post<SaleOrderStatusResponse>(
          '/ban-hang/bao-cao-ban-hang/bao-cao-tinh-trang-don-hang/',
          paginatedParams
        );

        const rawData = response.data.results || [];

        // Only add summary row on first page
        if (currentPage === 0) {
          // Create summary row and add it to the beginning
          const summaryRow = createSummaryRow(rawData, response.data.summary);
          const dataWithSummary = [summaryRow, ...rawData];
          setData(dataWithSummary);
        } else {
          setData(rawData);
        }

        setTotalItems(response.data.count || 0);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while posting data';
        setError(new Error(errorMessage));
        setData([]);
        setTotalItems(0);
      } finally {
        setIsLoading(false);
      }
    },
    [currentPage, pageSize]
  );

  const handlePageChange = useCallback(async (page: number) => {
    setCurrentPage(page);
    // The useEffect will automatically trigger fetchData when currentPage changes
  }, []);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    totalItems,
    currentPage,
    pageSize,
    fetchData,
    refreshData,
    postData,
    handlePageChange
  };
}
