import { GridRenderCellParams, GridColDef } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  boPhanSearchColumns,
  dotThanhToanSearchColumns,
  hopDongSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  vuViecSearchColumns,
  accountSearchColumns,
  khachHangSearchColumns,
  chiPhiKhongHopLeSearchColumns,
  thueSearchColumns,
  hanThanhToanSearchColumns,
  tinhChatThueSearchColumns,
  mauSoHoaDonSearchColumns
} from '@/constants';
import {
  BoPhan,
  ChiPhiKhongHopLeData,
  DotThanhToan,
  HanThanhToan,
  HopDong,
  KhachHang,
  KheUoc,
  MauSoHoaDon,
  Phi,
  TaiKhoan,
  Tax,
  TinhChatThue,
  VatTu,
  <PERSON>uViec
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';
import { FormMode } from '@/types/form';

export const getTaxTableColumns = (
  formMode: FormMode,
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'so_ct0',
    headerName: 'Số hóa đơn',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='so_ct0'
        type='text'
        value={params.row.so_ct0 || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'so_ct0', newValue)}
      />
    )
  },
  {
    field: 'so_ct2',
    headerName: 'Ký hiệu',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='so_ct2'
        type='text'
        value={params.row.so_ct2 || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'so_ct2', newValue)}
      />
    )
  },
  {
    field: 'ngay_ct0',
    headerName: 'Ngày hóa đơn',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='ngay_ct0'
        type='date'
        value={params.row.ngay_ct0 || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ngay_ct0', newValue)}
      />
    )
  },
  {
    field: 'ma_thue',
    headerName: 'Thuế suất',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<Tax>
        searchEndpoint={`/${QUERY_KEYS.THUE}/`}
        searchColumns={thueSearchColumns}
        columnDisplay='ma_thue'
        dialogTitle='Danh mục thuế suất'
        value={params.row.ma_thue_data?.ten_thue}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_thue_data', row)}
      />
    )
  },
  {
    field: 'ma_mau_ct',
    headerName: 'Mẫu hóa đơn',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<MauSoHoaDon>
        searchEndpoint={`/${QUERY_KEYS.MAU_SO_HOA_DON}/`}
        searchColumns={mauSoHoaDonSearchColumns}
        columnDisplay='ma_mau_so'
        dialogTitle='Danh mục mẫu chứng từ'
        value={params.row.ma_mau_ct_data?.ma_mau_so || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_mau_ct_data', row)}
      />
    )
  },
  {
    field: 'ma_mau_bc',
    headerName: 'Mẫu báo cáo',
    width: 150,
    renderCell: params => (
      <CellField
        name='ma_mau_bc'
        type='select'
        value={params.row.ma_mau_bc}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_mau_bc', newValue)}
        options={[
          { value: '3', label: '3. Hóa đơn giá trị gia tăng' },
          { value: '5', label: '5. Hóa đơn bán hàng thông thường' },
          { value: '4', label: '4. Hàng hóa dịch vụ mua không hóa đơn' }
        ]}
      />
    )
  },
  {
    field: 'ma_tc_thue',
    headerName: 'Mã tính chất',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<TinhChatThue>
        searchEndpoint={`/${QUERY_KEYS.TINH_CHAT_THUE}/`}
        searchColumns={tinhChatThueSearchColumns}
        columnDisplay='ma_tc_thue'
        dialogTitle='Danh mục mẫu chứng từ'
        value={params.row.ma_tc_thue_data?.ma_tc_thue || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_tc_thue_data', row)}
      />
    )
  },
  {
    field: 'ma_kh',
    headerName: 'Mã ncc',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<KhachHang>
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục khách hàng'
        value={params.row.ma_kh_data?.customer_code || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh_data', row)}
      />
    )
  },
  {
    field: 'ten_kh_thue',
    headerName: 'Tên nhà cung cấp',
    width: 200,
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='ten_kh_thue'
        type='text'
        value={params.row.ma_kh_data?.customer_name || params.row.ten_kh_thue || ''}
      />
    )
  },
  {
    field: 'dia_chi',
    headerName: 'Địa chỉ',
    width: 250,
    renderCell: (params: GridRenderCellParams) => (
      <CellField name='dia_chi' type='text' value={params.row.ma_kh_data?.address || params.row.dia_chi || ''} />
    )
  },
  {
    field: 'ma_so_thue',
    headerName: 'Mã số thuế',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <CellField name='ma_so_thue' type='text' value={params.row.ma_kh_data?.tax_code || params.row.ma_so_thue || ''} />
    )
  },
  {
    field: 'ten_vt_thue',
    headerName: 'Tên hàng hóa - dịch vụ',
    width: 200,
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='ten_vt_thue'
        type='text'
        value={params.row.ten_vt_thue || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ten_vt_thue', newValue)}
      />
    )
  },
  {
    field: 't_tien_nt',
    headerName: 'Tiền hàng VND',
    width: 120,
    type: 'number',
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='t_tien_nt'
        type='number'
        value={params.row.t_tien_nt}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 't_tien_nt', newValue)}
      />
    )
  },
  {
    field: 'tk_thue_no',
    headerName: 'Tk thuế',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<TaiKhoan>
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.tk_thue_no_data?.code || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_thue_no_data', row)}
      />
    )
  },
  {
    field: 'tk_du',
    headerName: 'Tk đối ứng',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<TaiKhoan>
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.tk_du_data?.code || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_du_data', row)}
      />
    )
  },
  {
    field: 't_thue_nt',
    headerName: 'Thuế VND',
    width: 120,
    type: 'number',
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='t_thue_nt'
        type='number'
        value={params.row.t_thue_nt || 0}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 't_thue_nt', newValue)}
      />
    )
  },
  {
    field: 'ma_kh9',
    headerName: 'Cục thuế',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<KhachHang>
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục đối tượng'
        value={params.row.ma_kh9_data?.customer_code || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh9_data', row)}
      />
    )
  },
  {
    field: 'ma_tt',
    headerName: 'Mã thanh toán',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<HanThanhToan>
        searchEndpoint={`/${QUERY_KEYS.HAN_THANH_TOAN}/`}
        searchColumns={hanThanhToanSearchColumns}
        columnDisplay='ma_tt'
        dialogTitle='Danh mục thanh toán'
        value={params.row.ma_tt_data?.ma_tt || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_tt_data', row)}
      />
    )
  },
  {
    field: 'ghi_chu',
    headerName: 'Ghi chú',
    width: 200,
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='ghi_chu'
        type='text'
        value={params.row.ghi_chu || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ghi_chu', newValue)}
      />
    )
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<BoPhan>
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        columnDisplay='ma_bp'
        dialogTitle='Danh mục bộ phận'
        value={params.row.ma_bp_data?.ma_bp || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_bp_data', row)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<VuViec>
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vu_viec'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vu_viec || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Mã hợp đồng',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<HopDong>
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<DotThanhToan>
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<KheUoc>
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<Phi>
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<VatTu>
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục sản phẩm'
        value={params.row.ma_sp_data?.ma_vt || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_sp_data', row)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 150,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<any>
        searchEndpoint={`/`}
        searchColumns={lenhSanXuatSearchColumns}
        columnDisplay='ma_lsx'
        dialogTitle='Danh mục lệnh sản xuất'
        value={params.row.ma_lsx_data?.ma_lsx || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<ChiPhiKhongHopLeData>
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
        searchColumns={chiPhiKhongHopLeSearchColumns}
        columnDisplay='ma_cp_khl'
        dialogTitle='Danh mục tài khoản'
        value={params.row.ma_cp0_data?.ma_cpkhl || ''}
        disabled={formMode === 'view'}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_cp0_data', row)}
      />
    )
  }
];
