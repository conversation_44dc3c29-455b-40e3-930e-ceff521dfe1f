import { transformDocumentNumber } from '@/components/custom/arito/form/document-number/util';
import { MA_CHUNG_TU } from '@/constants/ma-chung-tu';
import { isValidUUID } from '@/lib/uuid-validator';
import { FormFieldState } from '../hooks';

export const transformDetailRows = (detailRows: any[]) => {
  return detailRows.map((row: any, index: number) => ({
    line: index + 1,
    uuid: isValidUUID(row.uuid) ? row.uuid : null,

    // Product information
    ma_vt: row.ma_vt_data?.uuid || row.ma_vt || null,
    ten_vt: row.ma_vt_data?.ten_vt || row.ten_vt || null,
    dvt: row.ma_vt_data?.dvt || row.dvt || null,

    // Location information
    ma_kho: row.ma_kho_data?.uuid || row.ma_kho || null,
    ma_vi_tri: row.ma_vi_tri_data?.uuid || row.ma_vi_tri || null,

    // Quantity and pricing
    so_luong: Number(row.so_luong || 0),
    gia_nt2: Number(row.gia_nt2 || 0),
    tien_nt2: Number(row.tien_nt2 || 0),

    // Discount information
    tl_ck: Number(row.tl_ck || 0),
    ck_nt: Number(row.ck_nt || 0),

    // Tax information
    ma_thue: row.ma_thue_data?.uuid || row.ma_thue || null,
    tk_thue_no: row.tk_thue_no_data?.uuid || row.tk_thue_no || null,
    thue_suat: Number(row.ma_thue_data?.thue_suat || row.thue_suat || 0),
    thue_nt: Number(row.thue_nt || 0),

    // Account information
    tk_du: row.tk_du_data?.uuid || row.tk_du || null,
    tk_ck: row.tk_ck_data?.uuid || row.tk_ck || null,

    // Department and project information
    ma_bp: row.ma_bp_data?.uuid || row.ma_bp || null,
    ma_vv: row.ma_vv_data?.uuid || row.ma_vv || null,
    ma_hd: row.ma_hd_data?.uuid || row.ma_hd || null,
    ma_dtt: row.ma_dtt_data?.uuid || row.ma_dtt || null,
    ma_ku: row.ma_ku_data?.uuid || row.ma_ku || null,
    ma_phi: row.ma_phi_data?.uuid || row.ma_phi || null,
    ma_sp: row.ma_sp_data?.uuid || row.ma_sp || null,
    ma_lsx: row.ma_lsx_data?.uuid || row.ma_lsx || null,
    ma_cp0: row.ma_cp0_data?.uuid || row.ma_cp0 || null,

    // Invoice reference information
    so_ct_hd: row.so_ct_hd || null,
    line_hd: row.line_hd || null
  }));
};

export const transformFormData = (
  data: any,
  state: FormFieldState,
  tongSoLuong: number,
  tongTienNt2: number,
  tongChietKhau: number,
  tongThue: number,
  detailRows: any[],
  entityUnit: any
) => {
  // Transform detail rows
  const detail = transformDetailRows(detailRows);

  return {
    ...data,
    unit_id: entityUnit?.uuid || null,
    ty_gia: data.ty_gia ? parseFloat(data.ty_gia).toFixed(2) : 1.0,

    ma_kh: state.khachHang?.uuid || null,
    ma_so_thue: data.ma_so_thue || state.khachHang?.tax_code || null,
    ten_kh_thue: data.ten_kh_thue || state.khachHang?.customer_name || null,
    dia_chi: data.dia_chi || state.khachHang?.address || null,
    ong_ba: data.ong_ba || state.khachHang?.contact_person || null,
    e_mail: data.e_mail || state.khachHang?.email || null,
    dien_giai: data.dien_giai || state.khachHang?.description || null,
    ngay_ct: data.ngay_ct || new Date().toISOString().split('T')[0],
    ngay_lct: data.ngay_ct || new Date().toISOString().split('T')[0],

    // References

    tk: state.taiKhoan?.uuid || state.khachHang?.account_data?.uuid || null,
    ma_tt: state.hanThanhToan?.uuid || state.khachHang?.payment_term_data?.uuid || null,
    ma_nt: data.ma_nt || null,
    ...transformDocumentNumber(state.quyenChungTu, state.soChungTu, MA_CHUNG_TU.BAN_HANG.PHIEU_NHAP_HANG_BAN_TRA_LAI),
    ma_dc: state.diaChiGiaoHang?.uuid || null,

    // E-invoice information
    so_ct_hddt: data.so_ct_hddt || null,
    ngay_ct_hddt: data.ngay_ct_hddt || null,
    so_ct2_hddt: data.so_ct2_hddt || null,
    ma_mau_ct_hddt: data.ma_mau_ct_hddt || null,
    ma_tthddt: data.ma_tthddt || '0',
    ma_pttt: data.ma_pttt || null,

    // Status
    status: data.status || '5',
    t_so_luong: tongSoLuong,
    t_tien_nt2: tongTienNt2,
    t_ck_nt: tongChietKhau,
    t_thue_nt: tongThue,

    // Detail arrays
    chi_tiet: detail
  };
};
