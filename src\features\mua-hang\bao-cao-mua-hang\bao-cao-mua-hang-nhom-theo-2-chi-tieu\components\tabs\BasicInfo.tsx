import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { FormField } from '@/components/custom/arito/form/form-field';
import DocumentNumberRange from '../DocumentNumberRange';
import { Label } from '@/components/ui/label';

const BasicInfo: React.FC = () => {
  const reportByOptions = [
    { value: '200', label: 'Khách hàng' },
    { value: '210', label: 'Nhóm khách 1' },
    { value: '220', label: 'Nhóm khách 2' },
    { value: '230', label: 'Nhóm khách 3' },
    { value: '300', label: 'Vật tư' },
    { value: '320', label: 'Nhóm vật tư 1' },
    { value: '330', label: 'Nhóm vật tư 2' },
    { value: '340', label: 'Nhóm vật tư 3' },
    { value: '700', label: 'Đơn vị' },
    { value: '810', label: '<PERSON><PERSON> phận' },
    { value: '910', label: '<PERSON>h<PERSON> viên bán hàng' }
  ];

  const groupByOptions = [
    { value: '0', label: 'Không nhóm' },
    { value: '200', label: 'Khách hàng' },
    { value: '210', label: 'Nhóm khách 1' },
    { value: '220', label: 'Nhóm khách 2' },
    { value: '230', label: 'Nhóm khách 3' },
    { value: '300', label: 'Vật tư' },
    { value: '320', label: 'Nhóm vật tư 1' },
    { value: '330', label: 'Nhóm vật tư 2' },
    { value: '340', label: 'Nhóm vật tư 3' },
    { value: '700', label: 'Đơn vị' },
    { value: '810', label: 'Bộ phận' },
    { value: '820', label: 'Vụ việc' },
    { value: '830', label: 'Hợp đồng' },
    { value: '910', label: 'Nhân viên bán hàng' }
  ];

  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex w-[61%] items-center'>
          <Label className='w-40 min-w-40'>Ngày từ/đến:</Label>
          <div>
            <AritoFormDateRangeDropdown fromDateName='ngay_ct1' toDateName='ngay_ct2' />
          </div>
        </div>
        {/* Report By */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Báo cáo theo:</Label>
          <div className='w-[300px]'>
            <FormField
              name='detail_by'
              options={reportByOptions}
              defaultValue='200'
              placeholder='Chọn loại báo cáo'
              className='w-full'
              type='select'
            />
          </div>
        </div>

        {/* Group By */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm theo:</Label>
          <div className='w-[300px]'>
            <FormField
              name='group_by'
              options={groupByOptions}
              defaultValue=''
              placeholder='Chọn kiểu nhóm'
              className='w-full'
              type='select'
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
