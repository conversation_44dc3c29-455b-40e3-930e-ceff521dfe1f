import { useState } from 'react';
import { AccountModel, HanThanhToan, QuyenChungTu } from '@/types/schemas';

export interface FormFieldState {
  taiKhoan: AccountModel | null;
  quyenChungTu: QuyenChungTu | null;
  thanhToan: HanThanhToan | null;
  dienGiai: string | null;

  soChungTu: string;

  loaiPhieuChi: string;
}

export interface FormFieldActions {
  setTaiKhoan: (taiKhoan: AccountModel) => void;
  setQuyenChungTu: (quyenChungTu: QuyenChungTu) => void;
  setThanhToan: (thanhToan: HanThanhToan) => void;
  setSoChungTu: (soChungTu: string) => void;
  setDienGiai: (dienGiai: string | null) => void;

  setLoaiPhieuChi: (value: string) => void;

  // Utility functions
  resetState: () => void;
  updateState: (updates: Partial<FormFieldState>) => void;
}

export interface UseFormFieldStateReturn {
  state: FormFieldState;
  actions: FormFieldActions;
}

const initialState: FormFieldState = {
  taiKhoan: null,
  quyenChungTu: null,
  dienGiai: null,
  thanhToan: null,
  soChungTu: '',
  loaiPhieuChi: '1'
};

function transformInitialData(initialData?: any): Partial<FormFieldState> {
  if (!initialData) return {};

  return {
    taiKhoan: initialData.tk_data || null,
    dienGiai: initialData.dien_giai || null,
    quyenChungTu: initialData.ma_nk_data || null,
    soChungTu: initialData.so_ct || null,
    loaiPhieuChi: initialData.ma_ngv || '1'
  };
}

export function useFormFieldState(initialData?: any): UseFormFieldStateReturn {
  const [state, setState] = useState<FormFieldState>({
    ...initialState,
    ...transformInitialData(initialData)
  });

  const actions: FormFieldActions = {
    setTaiKhoan: (taiKhoan: AccountModel) => {
      setState(prev => ({ ...prev, taiKhoan }));
    },

    setQuyenChungTu: (quyenChungTu: QuyenChungTu) => {
      setState(prev => ({ ...prev, quyenChungTu }));
    },

    setSoChungTu: (soChungTu: string) => {
      setState(prev => ({ ...prev, soChungTu }));
    },

    setDienGiai: (dienGiai: string | null) => {
      setState(prev => ({ ...prev, dienGiai }));
    },

    setThanhToan: (thanhToan: HanThanhToan) => {
      setState(prev => ({ ...prev, thanhToan }));
    },

    setLoaiPhieuChi: (value: string) => {
      setState(prev => ({ ...prev, loaiPhieuChi: value }));
    },

    resetState: () => {
      setState(initialState);
    },

    updateState: (updates: Partial<FormFieldState>) => {
      setState(prev => ({ ...prev, ...updates }));
    }
  };

  return { state, actions };
}
