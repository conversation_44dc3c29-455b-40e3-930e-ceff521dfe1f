import { format } from 'date-fns';
import { z } from 'zod';

export const searchSchema = z.object({
  ngay_bc: z.string().min(1, '<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o là bắt buộc'),

  ma_lts: z.string().optional(),
  ma_bp: z.string().optional(),
  nh_ts1: z.string().optional(),
  nh_ts2: z.string().optional(),
  nh_ts3: z.string().optional(),
  ma_unit: z.string().optional(),
  mau_bc: z.string().optional(),
  data_analysis_struct: z.string().optional(),

  // Data objects for storing full object information
  ma_lts_data: z.any().optional(),
  ma_bp_data: z.any().optional(),
  nh_ts1_data: z.any().optional(),
  nh_ts2_data: z.any().optional(),
  nh_ts3_data: z.any().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  ngay_bc: format(new Date(), 'yyyy-MM-dd'),
  ma_lts: '',
  ma_bp: '',
  nh_ts1: '',
  nh_ts2: '',
  nh_ts3: '',
  ma_unit: '',
  mau_bc: '20',
  data_analysis_struct: '',

  // Initialize _data fields
  ma_lts_data: undefined,
  ma_bp_data: undefined,
  nh_ts1_data: undefined,
  nh_ts2_data: undefined,
  nh_ts3_data: undefined
};
