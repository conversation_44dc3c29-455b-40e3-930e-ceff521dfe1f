'use client';

import { useEffect } from 'react';
import Split from 'react-split';
import { getSupplierReturnReceiptColumns, getSupplierReturnReceiptDetailColumns } from './cols-definition';
import { InputTable, AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { ActionBar, FormDialog, InputTableAction, SearchDialog } from './components';
import ConfirmationDialog from '@/components/custom/arito/dialog/confirm-dialog';
import { useFormState, useCRUD } from '@/hooks';
import { QUERY_KEYS } from '@/constants';

export default function HoaDonDieuChinhGiaHangBan() {
  const {
    data,
    isLoading,
    addItem,
    updateItem,
    deleteItem,
    refreshData,
    fetchDetail,
    totalItems,
    currentPage,
    handlePageChange
  } = useCRUD<any, any>({
    endpoint: QUERY_KEYS.HOA_DON_DIEU_CHINH_GIA_HANG_BAN
  });
  const {
    showForm,
    showDelete,
    showSearch,
    formMode,
    isCopyMode,
    selectedObj,
    selectedRowIndex,
    handleRowClick,
    clearSelection,
    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleSearch,
    handleCloseSearch
  } = useFormState();

  useEffect(() => {
    fetchDetail(selectedObj?.uuid);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedObj?.uuid]);

  const handleSearchSubmit = (filters: any) => {
    return filters;
  };

  const handleSubmit = async (data: any) => {
    try {
      if (formMode === 'add') {
        await addItem(data);
      } else if (formMode === 'edit' && selectedObj) {
        await updateItem(selectedObj.uuid, data);
      }
      handleCloseForm();
      clearSelection();
      refreshData();
    } catch (error) {
      return;
    }
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: data,
      columns: getSupplierReturnReceiptColumns(handleViewClick)
    },
    {
      name: 'Lập chứng từ',
      rows: data?.filter(row => row.status === '0'),
      columns: getSupplierReturnReceiptColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#EAD1DC]' />
    },
    {
      name: 'Chờ duyệt',
      rows: data?.filter(row => row.status === '3'),
      columns: getSupplierReturnReceiptColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-red-500' />
    },
    {
      name: 'Xuất hóa đơn',
      rows: data?.filter(row => row.status === '5'),
      columns: getSupplierReturnReceiptColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#3D85C6]' />
    },
    {
      name: 'Khác',
      rows: data?.filter(row => row.status === '99'),
      columns: getSupplierReturnReceiptColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-black' />
    }
  ];

  return (
    <div className='flex h-full min-h-[calc(100vh-120px)] w-full flex-col'>
      {showSearch && <SearchDialog open={showSearch} onClose={handleCloseSearch} onSearch={handleSearchSubmit} />}

      {showForm && (
        <FormDialog
          formMode={formMode}
          open={showForm}
          onClose={handleCloseForm}
          initialData={formMode === 'add' && !isCopyMode ? undefined : selectedObj}
          onSubmit={handleSubmit}
          onAdd={handleAddClick}
          onEdit={handleEditClick}
          onDelete={handleDeleteClick}
          onCopy={handleCopyClick}
        />
      )}

      {!showForm && (
        <>
          <ActionBar
            onAdd={handleAddClick}
            onEdit={handleEditClick}
            onDelete={handleDeleteClick}
            onCopy={handleCopyClick}
            onSearch={handleSearch}
            onRefresh={async () => {
              await refreshData();
              await fetchDetail(selectedObj?.uuid || '');
            }}
            isEditDisabled={!selectedObj}
          />

          {isLoading && (
            <div className='flex h-full items-center justify-center'>
              <LoadingOverlay />
            </div>
          )}

          {!isLoading && (
            <Split
              className='flex flex-1 flex-col overflow-hidden'
              direction='vertical'
              sizes={[50, 50]}
              minSize={100}
              gutterSize={4}
              gutterAlign='center'
              snapOffset={30}
              dragInterval={1}
              cursor='row-resize'
            >
              <div className='w-full overflow-hidden'>
                <AritoDataTables
                  tables={tables}
                  onRowClick={handleRowClick}
                  selectedRowId={selectedRowIndex || undefined}
                  totalItems={totalItems}
                  currentPage={currentPage}
                  onPageChange={handlePageChange}
                  serverSidePagination={true}
                />
              </div>

              <div className='max-h-[300px] overflow-hidden'>
                <InputTable
                  rows={selectedObj?.chi_tiet_data || []}
                  columns={getSupplierReturnReceiptDetailColumns}
                  mode={formMode}
                  actionButtons={<InputTableAction formMode={formMode} handleExport={() => {}} handlePin={() => {}} />}
                />
              </div>
            </Split>
          )}
        </>
      )}
      {showDelete && (
        <ConfirmationDialog
          open={showDelete}
          onClose={handleCloseDelete}
          onConfirm={() => {
            deleteItem(selectedObj!.uuid);
            handleCloseDelete();
            clearSelection();
            handleCloseForm();
          }}
        />
      )}
    </div>
  );
}
