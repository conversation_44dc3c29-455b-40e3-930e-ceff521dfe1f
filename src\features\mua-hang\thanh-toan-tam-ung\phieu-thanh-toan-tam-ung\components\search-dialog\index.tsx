import React from 'react';
import { AritoForm, AritoIcon, AritoHeaderTabs, AritoDialog, BottomBar } from '@/components/custom/arito';
import { SearchFormValues, searchSchema } from '../../schema';
import BasicInfoTab from './BasicInfoTab';
import DetailTab from './DetailTab';
import FilterTab from './FilterTab';

interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (filters: any) => void;
}

const SearchDialog = ({ open, onClose, onSearch }: SearchDialogProps) => {
  const handleSubmit = (data: SearchFormValues) => {
    onSearch(data);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Lọc phiếu thanh toán tạm ứng'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        onSubmit={handleSubmit}
        className='w-[800px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode='add' />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'detail',
                  label: 'Chi tiết',
                  component: <DetailTab formMode='add' />
                }
              ]}
              className='border-b border-b-gray-200'
            />

            <FilterTab formMode='add' />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={<BottomBar onClose={onClose} mode='add' />}
      />
    </AritoDialog>
  );
};

export default SearchDialog;
