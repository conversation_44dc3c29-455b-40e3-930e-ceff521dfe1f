import { <PERSON><PERSON>, Log<PERSON>ut, <PERSON><PERSON><PERSON>, <PERSON>n, <PERSON>fresh<PERSON><PERSON><PERSON>, Trash, Save, Printer, Send, FilePlus2 } from 'lucide-react';
import React from 'react';
import { AritoActionButton } from '@/components/custom/arito';
import { FormMode } from '@/types/form';

/**
 * Get the title for the form dialog based on form mode and active tab
 */
export const getFormTitle = (formMode: FormMode, activeTab: string) => {
  if (formMode !== 'view') {
    return formMode === 'add' ? 'Mới' : 'Sửa';
  }

  switch (activeTab) {
    case 'info':
      return 'Phiếu kế toán';
    case 'history':
      return '<PERSON>ịch sử';
    case 'comment':
      return 'Bình luận';
    default:
      return 'Phiếu kế toán';
  }
};

/**
 * Get the action buttons for the form dialog based on form mode and active tab
 */
export const getFormActionButtons = (
  formMode: FormMode,
  activeTab: string,
  handlers: {
    onAdd?: () => void;
    onEdit?: () => void;
    onDelete?: () => void;
    onCopy?: () => void;
    handleClose: () => void;
  }
) => {
  const { onAdd, onEdit, onDelete, onCopy, handleClose } = handlers;

  if (formMode !== 'view') {
    return (
      <>
        <AritoActionButton title='Lưu' icon={Save} variant='secondary' type='submit' />
        <AritoActionButton title='Đóng' icon={LogOut} variant='destructive' onClick={handleClose} />
      </>
    );
  }

  switch (activeTab) {
    case 'info':
      return (
        <>
          <AritoActionButton title='In' icon={Printer} onClick={() => console.log('Print')} />
          <AritoActionButton title='Gửi xử lý' icon={Send} onClick={() => console.log('Send')} />
          <AritoActionButton title='Thêm' icon={FilePlus2} onClick={onAdd} />
          <AritoActionButton title='Sửa' icon={Pencil} onClick={onEdit} />
          <AritoActionButton title='Xóa' icon={Trash} onClick={onDelete} />
          <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopy} />
          <AritoActionButton title='Đóng' icon={LogOut} onClick={handleClose} />
        </>
      );
    case 'history':
      return (
        <>
          <AritoActionButton
            title='Refresh'
            variant='secondary'
            icon={RefreshCcw}
            onClick={() => console.log('refresh')}
          />
          <AritoActionButton title='Cố định cột' variant='secondary' icon={Pin} onClick={() => console.log('pin')} />
          <AritoActionButton title='Đóng' variant='secondary' icon={LogOut} onClick={handleClose} />
        </>
      );
    case 'comment':
      return (
        <>
          <AritoActionButton title='Đóng' variant='secondary' icon={LogOut} onClick={handleClose} />
        </>
      );
    default:
      return (
        <>
          <AritoActionButton title='Lưu' icon={Save} type='submit' />
          <AritoActionButton title='Đóng' icon={LogOut} onClick={handleClose} />
        </>
      );
  }
};

/**
 * Get form subtitle for PhieuKeToan
 */
export const getFormSubTitle = (): string => {
  return 'Phiếu kế toán';
};

/**
 * Check if form is in read-only mode
 */
export const isFormReadOnly = (formMode: FormMode): boolean => {
  return formMode === 'view';
};

/**
 * Get form validation rules based on form mode
 */
export const getFormValidationRules = (formMode: FormMode) => {
  const isReadOnly = isFormReadOnly(formMode);

  return {
    required: !isReadOnly,
    disabled: isReadOnly,
    validateOnChange: !isReadOnly,
    validateOnBlur: !isReadOnly
  };
};
