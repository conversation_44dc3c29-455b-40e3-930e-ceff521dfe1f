import { GridColDef } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  accountSearchColumns,
  boPhanSearchColumns,
  chiPhiKhongHopLeSearchColumns,
  chiPhiSearchColumns,
  dotThanhToanSearchColumns,
  hopDongSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  vuViecSearchColumns,
  customerSearchColumns,
  thueSearchColumns,
  hanThanhToanSearchColumns,
  tinhChatThueSearchColumns,
  mauSoHoaDonSearchColumns
} from '@/constants';
import {
  AccountModel,
  BoPhan,
  ChiPhi,
  ChiPhiKhongHopLeData,
  DotThanhToan,
  HopDong,
  KheUoc,
  Phi,
  VatTu,
  VuViec,
  <PERSON>ha<PERSON><PERSON><PERSON>,
  Tax,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>o<PERSON><PERSON><PERSON><PERSON>
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

export const getTaxTableColumns = (
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  // so_ct0 - Số chứng từ gốc
  {
    field: 'so_ct0',
    headerName: 'Số hoá đơn',
    width: 120,
    renderCell: params => (
      <CellField
        name='so_ct0'
        type='text'
        value={params.row.so_ct0 || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_ct0', newValue)}
      />
    )
  },
  // so_ct2 - Số chứng từ 2
  {
    field: 'so_ct2',
    headerName: 'Ký hiệu',
    width: 120,
    renderCell: params => (
      <CellField
        name='so_ct2'
        type='text'
        value={params.row.so_ct2 || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_ct2', newValue)}
      />
    )
  },
  // ngay_ct0 - Ngày chứng từ gốc
  {
    field: 'ngay_ct0',
    headerName: 'Ngày hoá đơn',
    width: 120,
    renderCell: params => (
      <CellField
        name='ngay_ct0'
        type='date'
        value={params.row.ngay_ct0 || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ngay_ct0', newValue)}
      />
    )
  },
  // ma_thue - Mã thuế
  {
    field: 'ma_thue',
    headerName: 'Thuế suất',
    width: 120,
    renderCell: params => (
      <SearchField<Tax>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.THUE}/`}
        searchColumns={thueSearchColumns}
        columnDisplay='ma_thue'
        dialogTitle='Danh mục thuế'
        value={params.row.ma_thue_data?.ma_thue || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_thue_data', row)}
      />
    )
  },
  // ma_mau_ct - Mã mẫu chứng từ
  {
    field: 'ma_mau_ct',
    headerName: 'Mẫu hoá đơn',
    width: 120,
    renderCell: params => (
      <SearchField<MauSoHoaDon>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.MAU_SO_HOA_DON}/`}
        searchColumns={mauSoHoaDonSearchColumns}
        dialogTitle='Danh mục mẫu hoá đơn'
        columnDisplay='ma_mau_so'
        value={params.row.ma_mau_ct_data?.ma_mau_so || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'ma_mau_ct_data', row);
        }}
      />
    )
  },
  // ma_mau_bc - Mã mẫu báo cáo
  {
    field: 'ma_mau_bc',
    headerName: 'Mẫu báo cáo',
    width: 120,
    renderCell: params => (
      <CellField
        name='ma_mau_bc'
        type='select'
        options={[
          { value: '3', label: '3. Hóa đơn giá trị gia tăng' },
          { value: '4', label: '4. Hàng hóa, dịch vụ mua vào không có hóa đơn' },
          { value: '5', label: '5. Hóa đơn bán hàng thông thường' }
        ]}
        value={params.row.ma_mau_bc || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_mau_bc', newValue)}
      />
    )
  },
  // ma_tc_thue - Mã tính chất thuế
  {
    field: 'ma_tc_thue',
    headerName: 'Mã tính chất',
    width: 120,
    renderCell: params => (
      <SearchField<TinhChatThue>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TINH_CHAT_THUE}/`}
        searchColumns={tinhChatThueSearchColumns}
        columnDisplay='ma_tc_thue'
        dialogTitle='Danh mục tính chất thuế'
        value={params.row.ma_tc_thue_data?.ma_tc_thue || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_tc_thue_data', row)}
      />
    )
  },
  // ma_kh - Mã khách hàng
  {
    field: 'ma_kh',
    headerName: 'Mã NCC',
    width: 120,
    renderCell: params => (
      <SearchField<KhachHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={customerSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục đối tượng'
        value={params.row.ma_kh_data?.customer_code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh_data', row)}
      />
    )
  },
  // ten_kh_thue - Tên khách hàng thuế
  {
    field: 'ten_kh_thue',
    headerName: 'Tên nhà cung cấp',
    width: 150,
    renderCell: params => (
      <CellField
        name='ten_kh_thue'
        type='text'
        value={params.row.ma_kh_data?.customer_name || params.row.ten_kh_thue || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ten_kh_thue', newValue)}
      />
    )
  },
  // dia_chi - Địa chỉ
  {
    field: 'dia_chi',
    headerName: 'Địa chỉ',
    width: 200,
    renderCell: params => (
      <CellField
        name='dia_chi'
        type='text'
        value={params.row.dia_chi || params.row.ma_kh_data?.address || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'dia_chi', newValue)}
      />
    )
  },
  // ma_so_thue - Mã số thuế
  {
    field: 'ma_so_thue',
    headerName: 'Mã số thuế',
    width: 120,
    renderCell: params => (
      <CellField
        name='ma_so_thue'
        type='text'
        value={params.row.ma_so_thue || params.row.ma_kh_data?.tax_code || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_so_thue', newValue)}
      />
    )
  },
  // ten_vt_thue - Tên vật tư thuế
  {
    field: 'ten_vt_thue',
    headerName: 'Tên hàng hóa - dịch vụ',
    width: 150,
    renderCell: params => (
      <CellField
        name='ten_vt_thue'
        type='text'
        value={params.row.ten_vt_thue || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ten_vt_thue', newValue)}
      />
    )
  },
  // t_tien_nt - Tổng tiền ngoại tệ
  {
    field: 't_tien_nt',
    headerName: 'Tiền hàng VND',
    width: 120,
    renderCell: params => (
      <CellField
        name='t_tien_nt'
        type='number'
        value={params.row.t_tien_nt || 0.0}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 't_tien_nt', newValue)}
      />
    )
  },
  // tk_thue_no - Tài khoản thuế nợ
  {
    field: 'tk_thue_no',
    headerName: 'TK thuế',
    width: 120,
    renderCell: params => (
      <SearchField<AccountModel>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản thuế nợ'
        value={params.row.tk_thue_no_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_thue_no_data', row)}
      />
    )
  },
  // t_thue_nt - Tổng thuế ngoại tệ
  {
    field: 't_thue_nt',
    headerName: 'Thuế VND',
    width: 120,
    renderCell: params => (
      <CellField
        name='t_thue_nt'
        type='number'
        value={params.row.t_thue_nt || 0.0}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 't_thue_nt', newValue)}
      />
    )
  },
  // ma_kh9 - Mã khách hàng 9
  {
    field: 'ma_kh9',
    headerName: 'Cục thuế',
    width: 120,
    renderCell: params => (
      <SearchField<KhachHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={customerSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục đối tượng'
        value={params.row.ma_kh9_data?.customer_code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh9_data', row)}
      />
    )
  },
  // ma_tt - Mã trạng thái
  {
    field: 'ma_tt',
    headerName: 'Mã thanh toán',
    width: 100,
    renderCell: params => (
      <SearchField<HanThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HAN_THANH_TOAN}/`}
        searchColumns={hanThanhToanSearchColumns}
        columnDisplay='ma_tt'
        dialogTitle='Danh mục thanh toán'
        value={params.row.ma_tt_data?.ma_tt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_tt_data', row)}
      />
    )
  },
  // ghi_chu - Ghi chú
  {
    field: 'ghi_chu',
    headerName: 'Ghi chú',
    width: 200,
    renderCell: params => (
      <CellField
        name='ghi_chu'
        type='text'
        value={params.row.ghi_chu || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ghi_chu', newValue)}
      />
    )
  },
  // ma_bp - Mã bộ phận
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => (
      <SearchField<BoPhan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        columnDisplay='ma_bp'
        dialogTitle='Danh mục bộ phận'
        value={params.row.ma_bp_data?.ma_bp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_bp_data', row)}
      />
    )
  },
  // ma_vv - Mã vụ việc
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: params => (
      <SearchField<VuViec>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vu_viec'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vu_viec || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_vv_data', row)}
      />
    )
  },
  // ma_hd - Mã hợp đồng
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: params => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_hd_data', row)}
      />
    )
  },
  // ma_dtt - Mã đợt thanh toán
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 120,
    renderCell: params => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_dtt_data', row)}
      />
    )
  },
  // ma_ku - Mã khế ước
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_ku_data', row)}
      />
    )
  },
  // ma_phi - Mã phí
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: params => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_phi_data', row)}
      />
    )
  },
  // ma_sp - Mã sản phẩm
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: params => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục sản phẩm'
        value={params.row.ma_sp_data?.ma_vt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_sp_data', row)}
      />
    )
  },
  // ma_lsx - Mã lệnh sản xuất
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 120,
    renderCell: params => (
      <SearchField<any>
        type='text'
        searchEndpoint={'/'}
        searchColumns={lenhSanXuatSearchColumns}
        columnDisplay='ma_lsx'
        dialogTitle='Danh mục lệnh sản xuất'
        value={params.row.ma_lsx_data?.ma_lsx || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_lsx_data', row)}
      />
    )
  },
  // ma_cp0 - Mã chi phí không hợp lệ
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 120,
    renderCell: params => (
      <SearchField<ChiPhiKhongHopLeData>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
        searchColumns={chiPhiKhongHopLeSearchColumns}
        columnDisplay='ma_cp_khl'
        dialogTitle='Danh mục chi phí không hợp lệ'
        value={params.row.ma_cp0_data?.ma_cp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_cp0_data', row)}
      />
    )
  }
];
