import { useState } from 'react';
import { AccountModel, Khach<PERSON>ang, QuyenChungTu } from '@/types/schemas';

export interface FormFieldState {
  khachHang: KhachHang | null;
  taiKhoan: AccountModel | null;
  quyenChungTu: QuyenChungTu | null;
  quyetToanTamUng: boolean;
  soChungTu: string;
  dienGiai: string;
}

export interface FormFieldActions {
  setKhachHang: (khachHang: KhachHang) => void;
  setTaiKhoan: (taiKhoan: AccountModel) => void;
  setQuyenChungTu: (quyenChungTu: QuyenChungTu) => void;

  setQuyetToanTamUng: (value: boolean) => void;

  setSoChungTu: (soChungTu: string) => void;
  setDienGiai: (dienGiai: string) => void;

  resetState: () => void;
  updateState: (updates: Partial<FormFieldState>) => void;
}

export interface UseFormFieldStateReturn {
  state: FormFieldState;
  actions: FormFieldActions;
}

const initialState: FormFieldState = {
  khachHang: null,
  taiKhoan: null,
  quyenChungTu: null,
  quyetToanTamUng: false,
  soChungTu: '',
  dienGiai: ''
};

function transformInitialData(initialData?: any): Partial<FormFieldState> {
  if (!initialData) return {};

  return {
    khachHang: initialData.ma_kh_data || null,
    taiKhoan: initialData.tk_data || null,
    quyenChungTu: initialData.so_ct_data || null,
    quyetToanTamUng: initialData.qt_tu_yn ?? false,
    soChungTu: initialData.so_ct || '',
    dienGiai: initialData.dien_giai || ''
  };
}

export function useFormFieldState(initialData?: any): UseFormFieldStateReturn {
  const [state, setState] = useState<FormFieldState>({
    ...initialState,
    ...transformInitialData(initialData)
  });

  const actions: FormFieldActions = {
    // Search field setters
    setKhachHang: (khachHang: KhachHang) => {
      setState(prev => ({
        ...prev,
        khachHang
      }));
    },

    setTaiKhoan: (taiKhoan: AccountModel) => {
      setState(prev => ({
        ...prev,
        taiKhoan
      }));
    },

    setQuyenChungTu: (quyenChungTu: QuyenChungTu) => {
      setState(prev => ({
        ...prev,
        quyenChungTu
      }));
    },

    setQuyetToanTamUng: (value: boolean) => {
      setState(prev => ({ ...prev, quyetToanTamUng: value }));
    },

    setSoChungTu: (soChungTu: string) => {
      setState(prev => ({ ...prev, soChungTu }));
    },

    setDienGiai: (dienGiai: string) => {
      setState(prev => ({ ...prev, dienGiai }));
    },

    resetState: () => {
      setState(initialState);
    },

    updateState: (updates: Partial<FormFieldState>) => {
      setState(prev => ({ ...prev, ...updates }));
    }
  };

  return { state, actions };
}
