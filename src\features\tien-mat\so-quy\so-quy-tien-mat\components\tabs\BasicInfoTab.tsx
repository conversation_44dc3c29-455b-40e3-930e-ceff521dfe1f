import React from 'react';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { FormField, SearchField } from '@/components/custom/arito';
import { accountSearchColumns, QUERY_KEYS } from '@/constants';
import { Label } from '@/components/ui/label';
import { TaiKhoan } from '@/types/schemas';
interface Props {
  setTaiKhoan: (tk: <PERSON>K<PERSON>) => void;
}
const BasicInfoTab: React.FC<Props> = ({ setTaiKhoan }) => {
  return (
    <div className='space-y-2 p-4 pb-7'>
      <div className='flex flex-col space-y-3'>
        {/* Tài khoản */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản</Label>
          <SearchField<TaiKhoan>
            type='text'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='code'
            displayRelatedField='name'
            onRowSelection={setTaiKhoan}
          />
        </div>
        {/* Date Range Fields */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ngày từ/đến</Label>
          <AritoFormDateRangeDropdown fromDateName='ngay_ct1' toDateName='ngay_ct2' />
        </div>
        {/* Date Open */}
        <div className='flex w-[70%] items-center'>
          <Label className='w-40 min-w-40'>Ngày mở sổ</Label>
          <FormField type='date' name='ngay_ms' />
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
