import React from 'react';
import {
  customerSearchColumns,
  loaiVatTuSearchColumns,
  nhomNhaCungCapSearchColumns,
  vatTuSearchColumns,
  warehouseSearchColumns,
  nhomColumns,
  QUERY_KEYS,
  khachHangSearchColumns
} from '@/constants';
import { FormField } from '@/components/custom/arito/form/form-field';
import { SearchField } from '@/components/custom/arito';
import { useSearchFieldStates } from '../../hooks';
import { Label } from '@/components/ui/label';
import { KhachHang } from '@/types/schemas';

interface DetailsTabProps {
  searchFieldStates?: ReturnType<typeof useSearchFieldStates>;
}

const DetailsTab: React.FC<DetailsTabProps> = ({ searchFieldStates }) => {
  return (
    <div className='w-[800px] min-w-[800px] space-y-4 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã khách hàng:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
              searchColumns={khachHangSearchColumns}
              dialogTitle='Danh mục khách hàng'
              columnDisplay='customer_code'
              displayRelatedField='customer_name'
              value={searchFieldStates?.customer?.customer_code || ''}
              relatedFieldValue={searchFieldStates?.customer?.customer_name || ''}
              onRowSelection={searchFieldStates?.setCustomer}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex w-[76%] items-center'>
          <Label className='w-40 min-w-40 text-left'>Nhóm khách hàng:</Label>
          <div className='flex-1'>
            <div className='grid grid-cols-3'>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
                  searchColumns={khachHangSearchColumns}
                  dialogTitle='Nhóm khách hàng'
                  columnDisplay='customer_code'
                  value={searchFieldStates?.customerGroup1?.ma_nhom || ''}
                  relatedFieldValue={searchFieldStates?.customerGroup1?.ten_phan_nhom || ''}
                  onRowSelection={searchFieldStates?.setCustomerGroup1}
                  className='w-full'
                />
              </div>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=NCC2`}
                  searchColumns={nhomColumns}
                  dialogTitle='Nhóm khách hàng'
                  columnDisplay='ma_nhom'
                  value={searchFieldStates?.customerGroup2?.ma_nhom || ''}
                  relatedFieldValue={searchFieldStates?.customerGroup2?.ten_phan_nhom || ''}
                  onRowSelection={searchFieldStates?.setCustomerGroup2}
                  className='w-full'
                />
              </div>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=NCC3`}
                  searchColumns={nhomColumns}
                  dialogTitle='Nhóm khách hàng'
                  columnDisplay='ma_nhom'
                  value={searchFieldStates?.customerGroup3?.ma_nhom || ''}
                  relatedFieldValue={searchFieldStates?.customerGroup3?.ten_phan_nhom || ''}
                  onRowSelection={searchFieldStates?.setCustomerGroup3}
                  className='w-full'
                />
              </div>
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã vật tư:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
              searchColumns={vatTuSearchColumns}
              dialogTitle='Vật tư'
              columnDisplay='ma_vt'
              displayRelatedField='ten_vt'
              value={searchFieldStates?.material?.ma_vt || ''}
              relatedFieldValue={searchFieldStates?.material?.ten_vt || ''}
              onRowSelection={searchFieldStates?.setMaterial}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Loại vật tư:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.LOAI_VAT_TU}/`}
              searchColumns={loaiVatTuSearchColumns}
              dialogTitle='Vật tư'
              columnDisplay='ma_lvt'
              displayRelatedField='ten_loai'
              value={searchFieldStates?.materialType?.ma_lvt || ''}
              relatedFieldValue={searchFieldStates?.materialType?.ten_loai || ''}
              onRowSelection={searchFieldStates?.setMaterialType}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex w-[76%] items-center'>
          <Label className='w-40 min-w-40 text-left'>Nhóm vật tư:</Label>
          <div className='flex-1'>
            <div className='grid grid-cols-3'>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=VT1`}
                  searchColumns={nhomNhaCungCapSearchColumns}
                  dialogTitle='Nhóm vật tư'
                  columnDisplay='ma_nhom'
                  value={searchFieldStates?.materialGroup1?.ma_nhom || ''}
                  onRowSelection={searchFieldStates?.setMaterialGroup1}
                  className='w-full'
                />
              </div>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=VT2`}
                  searchColumns={nhomNhaCungCapSearchColumns}
                  dialogTitle='Nhóm vật tư'
                  columnDisplay='ma_nhom'
                  value={searchFieldStates?.materialGroup2?.ma_nhom || ''}
                  onRowSelection={searchFieldStates?.setMaterialGroup2}
                  className='w-full'
                />
              </div>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=VT3`}
                  searchColumns={nhomNhaCungCapSearchColumns}
                  dialogTitle='Nhóm vật tư'
                  columnDisplay='ma_nhom'
                  value={searchFieldStates?.materialGroup3?.ma_nhom || ''}
                  onRowSelection={searchFieldStates?.setMaterialGroup3}
                  className='w-full'
                />
              </div>
            </div>
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã kho:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHO_HANG}/`}
              searchColumns={warehouseSearchColumns}
              dialogTitle='Kho hàng'
              columnDisplay='ma_kho'
              displayRelatedField='ten_kho'
              value={searchFieldStates?.warehouse?.ma_kho || ''}
              relatedFieldValue={searchFieldStates?.warehouse?.ten_kho || ''}
              onRowSelection={searchFieldStates?.setWarehouse}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Hạn giao hàng:</Label>
          <div className='w-32'>
            <FormField name='ngay_giao' type='date' className='w-full' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Trạng thái:</Label>
          <div className='w-64'>
            <FormField
              name='status'
              type='select'
              options={[
                { value: '__all__', label: 'Tất cả' },
                { value: '0', label: 'Lập chứng từ' },
                { value: '1', label: 'Chờ duyệt' },
                { value: '4', label: 'Đang duyệt' },
                { value: '5', label: 'Đã duyệt' },
                { value: '6', label: 'Đang thực hiện' },
                { value: '7', label: 'Hoàn thành' },
                { value: '9', label: 'Đóng' }
              ]}
              className='w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mẫu báo cáo:</Label>
          <div className='w-1/2'>
            <FormField
              name='mau_bc'
              type='select'
              defaultValue={20}
              options={[
                { value: 10, label: 'Mẫu số lượng' },
                { value: 20, label: 'Mẫu số lượng và giá trị' },
                { value: 30, label: 'Mẫu số lượng và giá trị ngoại tệ' }
              ]}
              className='w-full'
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
