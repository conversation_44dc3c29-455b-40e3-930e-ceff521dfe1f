import React from 'react';
import { reportFilterTemplateOptions, analysisTemplateOptions } from '../../schema';
import { FormField } from '@/components/custom/arito';
interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const OtherTab = ({ formMode }: Props) => {
  return (
    <div className='grid min-h-[100px] grid-cols-1 gap-x-8 space-y-2 p-4 lg:grid-cols-1 lg:space-y-0'>
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Mẫu lọc báo cáo'
        name='report_filtering'
        type='select'
        disabled={formMode === 'view'}
        options={reportFilterTemplateOptions}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Mẫu phân tích DL'
        name='data_analysis_struct'
        type='select'
        disabled={formMode === 'view'}
        options={analysisTemplateOptions}
      />
    </div>
  );
};

export default OtherTab;
