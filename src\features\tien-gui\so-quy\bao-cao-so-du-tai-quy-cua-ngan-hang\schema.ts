import { format } from 'date-fns';
import { z } from 'zod';

// Zod schemas for validation
export const BaoCaoSoDuTaiQuyCuaNganHangItemSchema = z.object({
  id: z.string(),
  account: z.string(),
  accountName: z.string(),
  debitBalance: z.number(),
  disabled: z.string()
});

export const BaoCaoSoDuTaiQuyCuaNganHangResponseSchema = z.object({
  count: z.number(),
  next: z.string().nullable(),
  previous: z.string().nullable(),
  results: z.array(BaoCaoSoDuTaiQuyCuaNganHangItemSchema)
});

export const searchSchema = z.object({
  // Basic Info Tab fields
  tk: z.string().optional(),
  ngay_ct: z.string().optional(),

  // Detail Tab fields
  mau_bc: z.number().optional(),

  // Other Tab fields
  report_filtering: z.string().optional(),
  data_analysis_struct: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;
export type BaoCaoSoDuTaiQuyCuaNganHangItem = z.infer<typeof BaoCaoSoDuTaiQuyCuaNganHangItemSchema>;
export type BaoCaoSoDuTaiQuyCuaNganHangResponse = z.infer<typeof BaoCaoSoDuTaiQuyCuaNganHangResponseSchema>;

export const initialValues: SearchFormValues = {
  tk: '',
  ngay_ct: format(new Date(), 'yyyy-MM-dd'),
  mau_bc: 20,
  report_filtering: '1',
  data_analysis_struct: '1'
};

// Report template options
export const reportTemplateOptions = [
  { label: 'Mẫu tiền chuẩn', value: 20 },
  { label: 'Mẫu ngoại tệ', value: 30 }
];

// Report filter template options
export const reportFilterTemplateOptions = [{ label: 'Người dùng tự lọc', value: '1' }];

// Analysis template options
export const analysisTemplateOptions = [{ label: 'Không phân tích', value: '1' }];
