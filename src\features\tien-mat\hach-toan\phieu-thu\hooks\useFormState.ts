import { useState } from 'react';
import { PhieuThuChiTiet, PhieuThuInput, PhieuThu } from '@/types/schemas';

export type FormMode = 'add' | 'edit' | 'view';

interface UseFormStateParams {
  selectedObj: PhieuThu | null;
  clearSelection: () => void;
  addPhieuThu: (data: PhieuThuInput) => Promise<PhieuThu>;
  updatePhieuThu: (uuid: string, data: PhieuThuInput) => Promise<PhieuThu>;
  deletePhieuThu: (uuid: string) => Promise<void>;
  refreshPhieuThus: () => Promise<void>;
  setSearchDialogOpen: (open: boolean) => void;
  setSearchFilters: (filters: Record<string, any>) => void;
}

export const useFormState = ({
  selectedObj,
  clearSelection,
  addPhieuThu,
  updatePhieuThu,
  deletePhieuThu,
  refreshPhieuThus,
  setSearchDialogOpen,
  setSearchFilters
}: UseFormStateParams) => {
  const [showForm, setShowForm] = useState(false);
  const [formMode, setFormMode] = useState<FormMode>('view');

  const handleFormSubmit = async (data: PhieuThuInput) => {
    try {
      if (formMode === 'add') {
        await addPhieuThu(data);
      } else if (formMode === 'edit' && selectedObj) {
        await updatePhieuThu(selectedObj.uuid, data);
      }

      setShowForm(false);
      clearSelection();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const handleAddClick = () => {
    setFormMode('add');
    setShowForm(true);
    clearSelection();
  };

  const handleEditClick = () => {
    if (selectedObj) {
      setFormMode('edit');
      setShowForm(true);
    }
  };

  const handleCopyClick = () => {
    if (selectedObj) {
      setFormMode('add');
      setShowForm(true);
    }
  };

  const handleDeleteClick = async () => {
    if (selectedObj) {
      try {
        await deletePhieuThu(selectedObj.uuid);
        clearSelection();
      } catch (error) {
        console.error('Error deleting phieu thu:', error);
      }
    }
  };

  const handleSearchClick = () => setSearchDialogOpen(true);
  const handleSearchClose = () => setSearchDialogOpen(false);
  const handleSearchSubmit = (filters: Record<string, any>) => {
    setSearchFilters(filters);
    setSearchDialogOpen(false);
  };

  const handleRefreshClick = () => refreshPhieuThus();
  const handlePrintClick = () => console.log('Print clicked');
  const handleFixedColumnsClick = () => console.log('Fixed columns clicked');
  const handleMultiPrintClick = () => console.log('Multi-print clicked');
  const handleExportDataClick = () => console.log('Export data clicked');
  const handleDownloadExcelTemplateClick = () => console.log('Download Excel template clicked');
  const handleImportFromExcelClick = () => console.log('Import from Excel clicked');

  return {
    showForm,
    setShowForm,
    formMode,
    handleFormSubmit,
    handleAddClick,
    handleEditClick,
    handleCopyClick,
    handleDeleteClick,
    handleSearchClick,
    handleSearchClose,
    handleSearchSubmit,
    handleRefreshClick,
    handlePrintClick,
    handleFixedColumnsClick,
    handleMultiPrintClick,
    handleExportDataClick,
    handleDownloadExcelTemplateClick,
    handleImportFromExcelClick
  };
};
