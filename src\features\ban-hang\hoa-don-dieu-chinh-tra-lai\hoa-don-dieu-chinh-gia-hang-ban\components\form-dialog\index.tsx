'use client';

import { useEffect, useMemo, useState } from 'react';
import { isArray } from 'lodash';
import { ActionButton, AritoForm, AritoHeaderTabs } from '@/components/custom/arito';
import { getFormTitle, getFormActionButtons, transformFormData } from '../../utils';
import ConfirmationDialog from '@/components/custom/arito/dialog/confirm-dialog';
import GetDocumentsInvoice from '@/components/custom/arito/form/get-documents';
import { calculateTotals, handleUpdateRowFields } from '../../utils/calc-util';
import { formSchema, FormSchema, initialFormValues } from '../../schema';
import { useFormFieldState, useInputTableRows } from '../../hooks';
import { useAuth } from '@/contexts/auth-context';
import InfoDropdown from './InfoDropdown';
import BasicInfoTab from './BasicInfoTab';
import { QUERY_KEYS } from '@/constants';
import EInvoiceTab from './EInvoiceTab';
import { FormMode } from '@/types/form';
import HistoryTab from './HistoryTab';
import BottomBar from './BottomBar';
import DetailTab from './DetailTab';
import OtherTab from './OtherTab';

interface FormDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: any;
  onSubmit?: (data: any) => void;
  onClose: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
}

const FormDialog = ({
  open,
  formMode,
  initialData,
  onClose,
  onSubmit,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}: FormDialogProps) => {
  const { entityUnit } = useAuth();
  const [openDialog, setOpenDialog] = useState('none');
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('info');
  const {
    rows: detailRows,
    selectedRowUuid: detailSelectedRowUuid,
    handleRowClick: detailHandleRowClick,
    handleAddRow: detailHandleAddRow,
    handleDeleteRow: detailHandleDeleteRow,
    handleCopyRow: detailHandleCopyRow,
    handlePasteRow: detailHandlePasteRow,
    handleMoveRow: detailHandleMoveRow,
    handleCellValueChange: detailHandleCellValueChange,
    setRows: setDetailRows
  } = useInputTableRows(initialData?.chi_tiet_data || [], undefined, handleUpdateRowFields);
  const { state, actions } = useFormFieldState(initialData);

  const { tongSoLuong, tongTienNt2, tongChietKhau, tongThue } = useMemo(() => {
    return calculateTotals(detailRows);
  }, [detailRows]);

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
      setActiveTab('info');
    }
  }, [open]);

  const handleSubmit = (data: any) => {
    const formData = transformFormData(
      data,
      state,
      tongSoLuong,
      tongTienNt2,
      tongChietKhau,
      tongThue,
      detailRows,
      entityUnit
    );
    onSubmit?.(formData);
    setIsFormDirty(false);
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setOpenDialog('confirm');
    } else {
      onClose();
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const handleSearchData = (searchedRows: any) => {
    const { xprocess, detailRows: searchedDetailRows } = searchedRows;
    let mapRows = [];
    if (xprocess === '1') mapRows = [...detailRows, ...searchedDetailRows];
    else mapRows = searchedDetailRows;
    const processedRows = mapRows.map((row: any, index: number) => ({
      ...row,
      uuid: row.uuid || `search-${index}-${Date.now()}`,
      line: index + 1
    }));
    setDetailRows(processedRows);
  };

  const title = getFormTitle(formMode, activeTab);
  const actionButtons = getFormActionButtons(formMode, activeTab, {
    onAdd,
    onEdit,
    onDelete,
    onCopy,
    handleClose
  });

  // Xử lý ngày lập chứng từ
  if (formMode === 'add' && initialData) {
    initialData = {
      ...initialData,
      ngay_ct: new Date().toISOString().split('T')[0],
      ngay_lct: new Date().toISOString().split('T')[0]
    };
  }
  return (
    <>
      <AritoForm<FormSchema>
        mode={formMode}
        title={title}
        actionButtons={actionButtons}
        subTitle='Hóa đơn điều chỉnh giá hàng bán'
        schema={formSchema}
        initialData={initialData || initialFormValues}
        onSubmit={handleSubmit}
        onClose={onClose}
        headerFields={
          <div onChange={() => setIsFormDirty(true)}>
            <AritoHeaderTabs
              tabs={[
                {
                  id: 'info',
                  label: 'Thông tin',
                  component: <BasicInfoTab formMode={formMode} formState={{ state, actions }} />
                },
                ...(formMode === 'view'
                  ? [
                      {
                        id: 'history',
                        label: 'Lịch sử',
                        component: <HistoryTab />
                      }
                    ]
                  : [])
              ]}
              onTabChange={handleTabChange}
              defaultTabIndex={activeTab === 'info' ? 0 : activeTab === 'history' ? 1 : 0}
              actionButtons={
                <>
                  {formMode !== 'view' && (
                    <ActionButton onClick={() => setOpenDialog('get-documents')} title='Hóa đơn bán hàng' icon={0} />
                  )}
                  {formMode !== 'add' && <InfoDropdown uuid={initialData?.uuid} />}
                </>
              }
            />
          </div>
        }
        tabs={
          activeTab === 'info' && [
            {
              id: 'details',
              label: 'Chi tiết',
              component: (
                <DetailTab
                  formMode={formMode}
                  rows={detailRows}
                  selectedRowUuid={detailSelectedRowUuid}
                  onRowClick={detailHandleRowClick}
                  onAddRow={detailHandleAddRow}
                  onDeleteRow={detailHandleDeleteRow}
                  onCopyRow={detailHandleCopyRow}
                  onPasteRow={detailHandlePasteRow}
                  onMoveRow={detailHandleMoveRow}
                  onCellValueChange={detailHandleCellValueChange}
                />
              )
            },
            {
              id: 'e-invoice',
              label: 'HĐĐT',
              component: <EInvoiceTab formMode={formMode} />
            },
            {
              id: 'other',
              label: 'Khác',
              component: <OtherTab formMode={formMode} formState={{ state, actions }} />
            }
          ]
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 '
        bottomBar={
          <BottomBar
            tongSoLuong={tongSoLuong}
            tongTienNt2={tongTienNt2}
            tongChietKhau={tongChietKhau}
            tongThue={tongThue}
          />
        }
      />
      {openDialog === 'confirm' && (
        <ConfirmationDialog
          onClose={() => setOpenDialog('none')}
          onConfirm={() => {
            setOpenDialog('none');
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          message='Bạn muốn kết thúc?'
        />
      )}
      {openDialog === 'get-documents' && (
        <GetDocumentsInvoice
          onSearch={handleSearchData}
          onClose={() => setOpenDialog('none')}
          chungTuType={QUERY_KEYS.HOA_DON_BAN_HANG}
        />
      )}
    </>
  );
};

export default FormDialog;
