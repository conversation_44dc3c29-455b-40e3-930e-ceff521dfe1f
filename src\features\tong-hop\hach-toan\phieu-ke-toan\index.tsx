'use client';

import { useState } from 'react';
import Split from 'react-split';
import { InputTable, AritoDataTables, DeleteDialog, LoadingOverlay } from '@/components/custom/arito';
import { PhieuKeToan, PhieuKeToanInput } from '@/types/schemas/phieu-ke-toan.type';
import { getTableColumns, getInputTableColumns } from './cols-definition';
import { ActionBar, FormDialog, InputTableAction } from './components';
import { useFormState, useRows, useToast, useCRUD } from '@/hooks';
import { usePhieuKeToanDetail } from './hooks';
import { QUERY_KEYS } from '@/constants';

export default function AccountingVoucherPage() {
  const [showSearchDialog, setShowSearchDialog] = useState(false);

  const {
    data: phieuKeToans,
    isLoading,
    addItem: addPhieuKeToan,
    updateItem: updatePhieuKeToan,
    deleteItem: deletePhieuKeToan,
    refreshData: refreshPhieuKeToans,
    totalItems,
    currentPage,
    handlePageChange
  } = useCRUD<PhieuKeToan, PhieuKeToanInput>({
    endpoint: QUERY_KEYS.PHIEU_KE_TOAN
  });
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();
  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,
    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();
  const { detail, fetchDetail } = usePhieuKeToanDetail(selectedObj?.uuid);

  const handleSubmit = async (data: PhieuKeToanInput) => {
    try {
      if (formMode === 'add') {
        await addPhieuKeToan(data);
      } else if (formMode === 'edit' && selectedObj) {
        await updatePhieuKeToan(selectedObj.uuid, data);
      }
      handleCloseForm();
      clearSelection();
      await refreshPhieuKeToans();
    } catch (error) {
      console.error(error);
    }
  };

  const handleSearch = () => {
    setShowSearchDialog(true);
  };

  const handleSearchClose = () => {
    setShowSearchDialog(false);
  };

  const handleSearchSubmit = (filters: any) => {
    console.log('Search filters:', filters);
    // TODO: Implement search functionality here
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: phieuKeToans,
      columns: getTableColumns(handleViewClick)
    },
    {
      name: 'Lập chứng từ',
      rows: phieuKeToans.filter((row: PhieuKeToan) => row.status === '0'),
      columns: getTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#EAD1DC]' />
    },
    {
      name: 'Chờ duyệt',
      rows: phieuKeToans.filter((row: PhieuKeToan) => row.status === '3'),
      columns: getTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-red-500' />
    },
    {
      name: 'Đã ghi sổ',
      rows: phieuKeToans.filter((row: PhieuKeToan) => row.status === '5'),
      columns: getTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#3D85C6]' />
    }
  ];

  return (
    <div className='flex h-full min-h-[calc(100vh-120px)] w-full flex-col overflow-hidden'>
      {showForm && (
        <FormDialog
          formMode={formMode}
          open={showForm}
          onClose={handleCloseForm}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : formMode === 'add' && !isCopyMode
                ? undefined
                : undefined
          }
          onSubmit={handleSubmit}
          onAdd={handleAddClick}
          onEdit={handleEditClick}
          onDelete={handleDeleteClick}
          onCopy={handleCopyClick}
        />
      )}

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteObj={deletePhieuKeToan}
          clearSelection={clearSelection}
        />
      )}

      {!showForm && (
        <>
          <ActionBar
            onAdd={handleAddClick}
            onEdit={handleEditClick}
            onDelete={handleDeleteClick}
            onCopy={handleCopyClick}
            onSearch={handleSearch}
            onRefresh={async () => {
              await refreshPhieuKeToans();
              await fetchDetail();
            }}
            isEditDisabled={!selectedObj}
          />

          {isLoading && (
            <div className='flex h-full items-center justify-center'>
              <LoadingOverlay />
            </div>
          )}

          {!isLoading && (
            <Split
              className='flex flex-1 flex-col overflow-hidden'
              direction='vertical'
              sizes={[50, 50]}
              minSize={200}
              gutterSize={4}
              gutterAlign='center'
              snapOffset={30}
              dragInterval={1}
              cursor='row-resize'
            >
              <div className='w-full overflow-hidden'>
                <AritoDataTables
                  tables={tables}
                  selectedRowId={selectedRowIndex || undefined}
                  onRowClick={handleRowClick}
                  pageSize={10}
                  totalItems={totalItems}
                  currentPage={currentPage}
                  onPageChange={handlePageChange}
                  serverSidePagination={true}
                />
              </div>

              <div className='max-h-[300px] overflow-hidden'>
                <InputTable
                  rows={(detail as any) || []}
                  columns={getInputTableColumns(detail as any)}
                  mode={formMode}
                  actionButtons={
                    <InputTableAction
                      formMode={formMode}
                      handleExport={() => console.log('Export clicked')}
                      handlePin={() => console.log('Pin clicked')}
                    />
                  }
                />
              </div>
            </Split>
          )}
        </>
      )}
    </div>
  );
}
