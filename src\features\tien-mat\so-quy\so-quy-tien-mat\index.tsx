'use client';

import React, { useState } from 'react';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { ActionBar, InitialSearchDialog } from './components';
import { LoadingOverlay } from '@/components/custom/arito';
import { cashBookColumns } from './cols-definition';
import { QUERY_KEYS } from '@/constants';
import { useFormState } from './hooks';
import { useReport } from '@/hooks';

export default function CashBookPage() {
  const { getReport, isLoading, data } = useReport<any, any>({
    endpoint: QUERY_KEYS.SO_QUY_TIEN_MAT
  });

  const { showForm, handleOpenForm, handleCloseForm } = useFormState();
  const [searchData, setSearchData] = useState();
  const handleSubmit = async (data: any) => {
    setSearchData(data);
    const { tk, ...formData } = data;
    try {
      await getReport({ ...formData, tk: tk.uuid });
      handleCloseForm();
    } catch (err) {
      return;
    }
  };

  const tables = [
    {
      name: '',
      rows: data,
      columns: cashBookColumns
    }
  ];
  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm && <InitialSearchDialog onClose={handleCloseForm} onSearch={handleSubmit} open />}

      <div className='w-full'>
        <ActionBar
          onSearchClick={handleOpenForm}
          onRefreshClick={() => {}}
          onEditPrintTemplateClick={() => {}}
          onExportDataClick={() => {}}
          onFixedColumnsClick={() => {}}
          onPrintClick={() => {}}
          searchParams={searchData}
        />
        {isLoading && <LoadingOverlay />}
        {!isLoading && <AritoDataTables tables={tables} />}
      </div>
    </div>
  );
}
