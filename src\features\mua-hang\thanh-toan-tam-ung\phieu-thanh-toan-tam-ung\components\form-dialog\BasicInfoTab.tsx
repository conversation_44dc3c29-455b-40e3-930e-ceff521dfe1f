import { useFormContext } from 'react-hook-form';
import { useEffect } from 'react';
import {
  QUERY_KEYS,
  MA_CHUNG_TU,
  accountSearchColumns,
  supplierSearchColumns,
  quyenChungTuSearchColumns
} from '@/constants';
import { SearchField, FormField, AritoIcon, DocumentNumberField, CurrencyInput } from '@/components/custom/arito';
import { AccountModel, KhachHang, QuyenChungTu } from '@/types/schemas';
import { FormFieldState, FormFieldActions } from '../../hooks';
import { useNgoaiTe, useQuyenChungTuByChungTu } from '@/hooks';
import { generateSoChungTuHienTai } from '@/lib/stringUtil';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
  formState: {
    state: FormFieldState;
    actions: FormFieldActions;
  };
}

export default function BasicInfoTab({ formMode, formState: { state, actions } }: BasicInfoTabProps) {
  const { watch } = useFormContext();
  const dienGiaiValue = watch('dien_giai');

  useEffect(() => {
    if (dienGiaiValue !== state.dienGiai) {
      actions.setDienGiai(dienGiaiValue || '');
    }
  }, [dienGiaiValue, state.dienGiai, actions]);

  return (
    <div className='flex flex-col gap-3 p-4'>
      {/* Customer Information Section */}
      <div className='grid grid-cols-12 gap-2'>
        {/* Left Column */}
        <div className='col-span-6 space-y-1'>
          <div className='flex items-center'>
            <Label className='w-36'>Loại phiếu thu</Label>
            <div className='w-[250px]'>
              <FormField
                name='ma_ngv'
                type='select'
                disabled={formMode === 'view'}
                options={[
                  { value: '2', label: '2. Tạm ứng thông thường' },
                  { value: '3', label: '3. Cấn trừ công nợ' }
                ]}
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-36'>Mã khách hàng</Label>
            <SearchField<KhachHang>
              type='text'
              name='ma_kh'
              value={state.khachHang?.customer_code || ''}
              searchEndpoint={`/${QUERY_KEYS.NHA_CUNG_CAP}/`}
              searchColumns={supplierSearchColumns}
              onRowSelection={actions.setKhachHang}
              columnDisplay='customer_code'
              dialogTitle='Danh mục khách hàng'
              className='w-[205px]'
              disabled={formMode === 'view'}
            />

            <div className='ml-4 flex items-center'>
              <Label className='w-36'>Mã số thuế</Label>
              <FormField
                name='ma_so_thue'
                type='text'
                disabled={formMode === 'view'}
                placeholder='Nhập và tra cứu'
                value={state.khachHang?.tax_code || ''}
              />
              <button className='ml-2 flex items-center justify-center border border-gray-200 px-3 py-1.5'>
                <AritoIcon icon={15} className='shrink-0' />
              </button>
              <button className='ml-2 flex items-center justify-center border border-gray-200 px-3 py-1.5'>
                <AritoIcon icon={888} />
              </button>
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-36'>Tên khách hàng</Label>
            <div className='flex-1'>
              <FormField
                name='ten_kh_thue'
                type='text'
                disabled={formMode === 'view'}
                value={state.khachHang?.customer_name || ''}
                placeholder='Tên khách hàng/đơn vị'
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-36'>Địa chỉ</Label>
            <div className='flex-1'>
              <FormField
                name='dia_chi'
                type='text'
                disabled={formMode === 'view'}
                labelClassName='w-48'
                value={state.khachHang?.address || ''}
                placeholder='Địa chỉ khách hàng'
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-36'>Tài khoản có</Label>
            <SearchField<AccountModel>
              type='text'
              name='tk'
              value={state.taiKhoan?.code || ''}
              relatedFieldValue={state.taiKhoan?.name || ''}
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              columnDisplay='code'
              displayRelatedField='name'
              searchColumns={accountSearchColumns}
              dialogTitle='Danh sách tài khoản'
              className='ml-1 max-w-sm flex-1'
              onRowSelection={actions.setTaiKhoan}
              disabled={formMode === 'view'}
            />
          </div>

          <div className='flex items-center'>
            <div className='flex-1'>
              <FormField
                label='Diễn giải'
                name='dien_giai'
                type='text'
                disabled={formMode === 'view'}
                labelClassName='w-36'
              />
            </div>
          </div>
        </div>

        {/* Middle Column */}
        <div className='col-span-3 space-y-1'>
          <div className='ml-4 flex h-8 items-center'></div>
          <div className='ml-4 flex h-8 items-center'></div>

          <div className='ml-4 flex items-center'>
            <FormField
              name='ong_ba'
              label='Người nhận'
              labelClassName='w-24'
              inputClassName='w-6 text-red-600'
              disabled={formMode === 'view'}
              className='w-[350px]'
              value={state.khachHang?.contact_person || ''}
              placeholder='Nhập tên người giao'
            />
          </div>

          <div className='ml-4 flex items-center'>
            <FormField
              name='e_mail'
              label='Email'
              labelClassName='w-24'
              inputClassName='w-64'
              className='w-[350px]'
              disabled={formMode === 'view'}
              value={state.khachHang?.email || ''}
              placeholder='Email'
            />
          </div>
        </div>

        {/* Right Column */}
        <div className='col-span-3 flex'>
          {/* Tab Content */}
          <div className='flex-1 pr-2'>
            <DocumentNumberField
              ma_ct={MA_CHUNG_TU.MUA_HANG.PHIEU_THANH_TOAN_TAM_UNG}
              quyenChungTu={state.quyenChungTu}
              onQuyenChungTuChange={actions.setQuyenChungTu}
              soChungTu={state.soChungTu}
              onSoChungTuChange={actions.setSoChungTu}
              disabled={formMode === 'view'}
              classNameSearchField='w-full'
            />

            <div className='flex items-center'>
              <Label className='w-32'>Ngày chứng từ</Label>
              <div className='flex-1'>
                <FormField name='ngay_ct' type='date' disabled={formMode === 'view'} labelClassName='w-32 shrink-0' />
              </div>
            </div>

            <CurrencyInput formMode={formMode} classNameInput='w-full' />

            <div className='flex items-center'>
              <Label className='w-32'>Trạng thái</Label>
              <div className='flex-1'>
                <FormField
                  name='status'
                  type='select'
                  disabled={formMode === 'view'}
                  options={[
                    { value: '0', label: 'Chưa ghi sổ' },
                    { value: '3', label: 'Chờ duyêt' },
                    { value: '5', label: 'Đã ghi sổ' },
                    { value: '9', label: 'Huỷ' }
                  ]}
                  defaultValue={'5'}
                />
              </div>
            </div>

            <div className='mt-1 flex'>
              <div className='mb-4 h-2 w-32 shrink-0' />
              <FormField
                label='Dữ liệu nhận được'
                name='transfer_yn'
                type='checkbox'
                disabled={true}
                labelClassName='w-32'
                defaultValue={true}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
