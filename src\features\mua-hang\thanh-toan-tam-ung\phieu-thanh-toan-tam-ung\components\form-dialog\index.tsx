'use client';

import React, { useEffect, useState, useMemo, useRef } from 'react';
import { getFormTitle, getFormActionButtons, transformFormData, calculateTotals } from '../../utils';
import { useDetailRows, useAdvSettlementRows, useTaxRows } from './hooks';
import { AritoForm, AritoHeaderTabs } from '@/components/custom/arito';
import { FormSchema, initialFormValues } from '../../schema';
import { useAuth } from '@/contexts/auth-context';
import AdvSettlementTab from './AdvSettlementTab';
import { useFormFieldState } from '../../hooks';
import ConfirmDialog from '../ConfirmDialog';
import BasicInfoTab from './BasicInfoTab';
import { FormMode } from '@/types/form';
import HistoryTab from './HistoryTab';
import BottomBar from './BottomBar';
import DetailTab from './DetailTab';
import { useToast } from '@/hooks';
import OtherTab from './OtherTab';
import TaxTab from './TaxTab';

interface FormDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: any;
  onSubmit?: (data: any) => void;
  onClose: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
}

const FormDialog = ({
  open,
  formMode,
  initialData,
  onClose,
  onSubmit,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}: FormDialogProps) => {
  const { entityUnit } = useAuth();
  const { toast } = useToast();
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('info');

  // Khởi tạo state trước để sử dụng trong useDetailRows
  const { state, actions } = useFormFieldState(initialData);

  const {
    rows: detailRows,
    selectedRowUuid: detailSelectedRowUuid,
    handleRowClick: detailHandleRowClick,
    handleAddRow: detailHandleAddRow,
    handleDeleteRow: detailHandleDeleteRow,
    handleCopyRow: detailHandleCopyRow,
    handlePasteRow: detailHandlePasteRow,
    handleMoveRow: detailHandleMoveRow,
    handleCellValueChange: detailHandleCellValueChange,
    updateDienGiaiForEmptyRows
  } = useDetailRows(initialData?.chi_tiet_phieu_thanh_toan_data || [], state.dienGiai);
  const {
    rows: advSettlementRows,
    selectedRowUuid: advSettlementSelectedRowUuid,
    handleRowClick: advSettlementHandleRowClick,
    handleAddRow: advSettlementHandleAddRow,
    handleDeleteRow: advSettlementHandleDeleteRow,
    handleCopyRow: advSettlementHandleCopyRow,
    handlePasteRow: advSettlementHandlePasteRow,
    handleMoveRow: advSettlementHandleMoveRow,
    handleCellValueChange: advSettlementHandleCellValueChange
  } = useAdvSettlementRows(initialData?.quyet_toan_tam_ung_data || []);
  const {
    rows: taxRows,
    selectedRowUuid: taxSelectedRowUuid,
    handleRowClick: taxHandleRowClick,
    handleAddRow: taxHandleAddRow,
    handleDeleteRow: taxHandleDeleteRow,
    handleCopyRow: taxHandleCopyRow,
    handleMoveRow: taxHandleMoveRow,
    handleCellValueChange: taxHandleCellValueChange
  } = useTaxRows(initialData?.thue_phieu_thanh_toan_data || []);

  const totals = useMemo(() => {
    return calculateTotals(detailRows, taxRows);
  }, [detailRows, taxRows]);

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
      setActiveTab('info');
    }
  }, [open]);

  // Tự động tạo row đầu tiên khi form mode là 'add' và chưa có rows
  useEffect(() => {
    if (formMode === 'add' && detailRows.length === 0) {
      detailHandleAddRow();
    }
  }, [formMode, detailRows.length, detailHandleAddRow]);

  // Sử dụng ref để theo dõi giá trị trước đó
  const prevDienGiaiRef = useRef<string>('');

  // Đồng bộ Diễn giải từ BasicInfoTab sang DetailTab (chỉ cho rows trống)
  useEffect(() => {
    if (state.dienGiai && state.dienGiai !== prevDienGiaiRef.current) {
      prevDienGiaiRef.current = state.dienGiai;
      updateDienGiaiForEmptyRows(state.dienGiai);
    }
  }, [state.dienGiai, updateDienGiaiForEmptyRows]);

  const handleSubmit = async (data: any) => {
    const mode = formMode === 'add' ? 'create' : formMode === 'edit' ? 'edit' : 'view';
    const formData = transformFormData({
      data,
      state,
      detailRows,
      taxRows,
      advSettlementRows,
      entityUnit,
      initialData,
      formMode: mode,
      includeEmptyArrays: false // Don't include empty arrays to reduce payload size
    });

    // Apply calculated totals
    const finalFormData = {
      ...formData,
      t_tien_nt: totals.totalAmount,
      t_tien: totals.totalAmount,
      t_thue_nt: totals.totalTax,
      t_thue: totals.totalTax,
      t_tt_nt: totals.totalPayment,
      t_tt: totals.totalPayment,
      t_tien_cl_nt: totals.totalAmount,
      t_tien_cl: totals.totalPayment
    };

    console.log('Final form data:', finalFormData);

    try {
      await onSubmit?.(finalFormData);
      setIsFormDirty(false);
    } catch (error: any) {
      console.error('Submit failed:', error);
    }
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const title = getFormTitle(formMode, activeTab);
  const actionButtons = getFormActionButtons(formMode, activeTab, {
    onAdd,
    onEdit,
    onDelete,
    onCopy,
    handleClose
  });

  return (
    <>
      <AritoForm<FormSchema>
        mode={formMode}
        title={title}
        actionButtons={actionButtons}
        subTitle='Phiếu thanh toán tạm ứng'
        schema={FormSchema}
        initialData={initialData || initialFormValues}
        onSubmit={handleSubmit}
        onClose={handleClose}
        headerFields={
          <div onChange={() => setIsFormDirty(true)}>
            <AritoHeaderTabs
              tabs={[
                {
                  id: 'info',
                  label: 'Thông tin',
                  component: <BasicInfoTab formMode={formMode} formState={{ state, actions }} />
                },
                ...(formMode === 'view'
                  ? [
                      {
                        id: 'history',
                        label: 'Lịch sử',
                        component: <HistoryTab />
                      }
                    ]
                  : [])
              ]}
              onTabChange={handleTabChange}
              defaultTabIndex={activeTab === 'info' ? 0 : activeTab === 'history' ? 1 : 0}
            />
          </div>
        }
        tabs={
          activeTab === 'info' && [
            {
              id: 'details',
              label: 'Chi tiết',
              component: (
                <DetailTab
                  formMode={formMode}
                  rows={detailRows}
                  selectedRowUuid={detailSelectedRowUuid}
                  onRowClick={detailHandleRowClick}
                  onAddRow={detailHandleAddRow}
                  onDeleteRow={detailHandleDeleteRow}
                  onCopyRow={detailHandleCopyRow}
                  onPasteRow={detailHandlePasteRow}
                  onMoveRow={detailHandleMoveRow}
                  onCellValueChange={detailHandleCellValueChange}
                />
              )
            },
            {
              id: 'tax',
              label: 'Thuế',
              component: (
                <TaxTab
                  formMode={formMode}
                  rows={taxRows}
                  selectedRowUuid={taxSelectedRowUuid}
                  onRowClick={taxHandleRowClick}
                  onAddRow={taxHandleAddRow}
                  onDeleteRow={taxHandleDeleteRow}
                  onCopyRow={taxHandleCopyRow}
                  onMoveRow={taxHandleMoveRow}
                  onCellValueChange={taxHandleCellValueChange}
                />
              )
            },
            ...(state.quyetToanTamUng
              ? [
                  {
                    id: 'adv-settlement',
                    label: 'Quyết toán các lần tạm ứng',
                    component: (
                      <AdvSettlementTab
                        formMode={formMode}
                        rows={advSettlementRows}
                        selectedRowUuid={advSettlementSelectedRowUuid}
                        onRowClick={advSettlementHandleRowClick}
                        onAddRow={advSettlementHandleAddRow}
                        onDeleteRow={advSettlementHandleDeleteRow}
                        onCopyRow={advSettlementHandleCopyRow}
                        onPasteRow={advSettlementHandlePasteRow}
                        onMoveRow={advSettlementHandleMoveRow}
                        onCellValueChange={advSettlementHandleCellValueChange}
                      />
                    )
                  }
                ]
              : []),
            {
              id: 'other',
              label: 'Khác',
              component: <OtherTab formMode={formMode} />
            }
          ]
        }
        bottomBar={
          <BottomBar
            totalAmount={totals.totalAmount}
            totalTax={totals.totalTax}
            totalPayment={totals.totalPayment}
            formState={{ state, actions }}
          />
        }
      />

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default FormDialog;
