import { GridCellParams } from '@mui/x-data-grid';
import React from 'react';
import { InputTable } from '@/components/custom/arito';
import { getDetailTableColumns } from './columns';
import { SelectedDetailCellInfo } from '../hooks';
import { FormMode } from '@/types/form';
import ActionBar from './ActionBar';

interface DetailTabProps {
  formMode: FormMode;
  rows: { uuid?: string | null }[];
  selectedRowUuid?: string | null;
  selectedCell?: SelectedDetailCellInfo | null;
  onRowClick?: (params: any) => void;
  onCellClick?: (params: GridCellParams) => void;
  onAddRow: () => void;
  onDeleteRow: () => void;
  onCopyRow: () => void;
  onPasteRow: () => void;
  onMoveRow: (direction: 'up' | 'down') => void;
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
}

export const DetailTab: React.FC<DetailTabProps> = ({
  formMode,
  rows,
  selectedRowUuid,
  onRowClick,
  onAddRow,
  onDeleteRow,
  onCopyRow,
  onPasteRow,
  onMoveRow,
  onCellValueChange
}) => {
  return (
    <InputTable
      rows={rows}
      onRowClick={onRowClick}
      selectedRowId={selectedRowUuid || undefined}
      columns={getDetailTableColumns(formMode, onCellValueChange)}
      getRowId={row => row?.uuid || ''}
      actionButtons={
        <ActionBar
          formMode={formMode}
          handleAddRow={onAddRow}
          handleDeleteRow={onDeleteRow}
          handleCopyRow={onCopyRow}
          handlePasteRow={onPasteRow}
          handleMoveRow={onMoveRow}
          handleExport={() => console.log('Export clicked')}
          handlePin={() => console.log('Pin clicked')}
        />
      }
    />
  );
};
