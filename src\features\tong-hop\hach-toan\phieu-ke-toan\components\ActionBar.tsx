import { <PERSON><PERSON>, <PERSON>cil, Plus, Trash, Printer, Search } from 'lucide-react';
import { AritoActionButton, AritoMenuButton, AritoActionBar, AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
  onPrint?: () => void;
  onSearch?: () => void;
  onRefresh?: () => void;
  isEditDisabled?: boolean;
}

export default function ActionBar({
  onAdd,
  onEdit,
  onDelete,
  onCopy,
  onPrint,
  onSearch,
  onRefresh,
  isEditDisabled = true
}: ActionBarProps) {
  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Phiếu kế toán</h1>}>
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAdd} />
      <AritoActionButton title='Sửa' icon={Pencil} onClick={onEdit} disabled={isEditDisabled} />
      <AritoActionButton title='Xóa' icon={Trash} onClick={onDelete} disabled={isEditDisabled} />
      <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopy} disabled={isEditDisabled} />
      <AritoMenuButton
        title='In ấn'
        icon={Printer}
        items={[
          {
            title: 'Mẫu chuẩn',
            icon: <AritoIcon icon={20} />,
            onClick: onPrint
          },
          {
            title: 'Mẫu chuẩn',
            icon: <AritoIcon icon={555} />,
            onClick: onPrint
          }
        ]}
      />
      <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearch} />

      <AritoMenuButton
        items={[
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: onRefresh,
            group: 1
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'In nhiều',
            icon: <AritoIcon icon={883} />,
            onClick: () => {},
            group: 2
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: () => {},
            group: 2
          },
          {
            title: 'Tải mẫu Excel',
            icon: <AritoIcon icon={28} />,
            onClick: () => {},
            group: 3
          },
          {
            title: 'Lấy dữ liệu từ Excel',
            icon: <AritoIcon icon={29} />,
            onClick: () => {},
            group: 3
          }
        ]}
      />
    </AritoActionBar>
  );
}
