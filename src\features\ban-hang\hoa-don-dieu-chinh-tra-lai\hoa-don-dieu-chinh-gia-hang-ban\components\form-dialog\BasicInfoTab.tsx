import {
  QUERY_KEYS,
  MA_CHUNG_TU,
  accountSearchColumns,
  khachHangSearchColumns,
  hanThanhToanSearchColumns,
  mauSoHoaDonSearchColumns,
  nhanVienSearchColumns
} from '@/constants';
import { SearchField, FormField, AritoIcon, DocumentNumberField, CurrencyInput } from '@/components/custom/arito';
import { AccountModel, HanThanhToan, KhachHang, MauSoHoaDon, NhanVien } from '@/types/schemas';
import { FormFieldState, FormFieldActions } from '../../hooks';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
  formState: {
    state: FormFieldState;
    actions: FormFieldActions;
  };
}

export default function BasicInfoTab({ formMode, formState: { state, actions } }: BasicInfoTabProps) {
  return (
    <div className='flex flex-col gap-3 p-4'>
      {/* Customer Information Section */}
      <div className='grid grid-cols-12 gap-6'>
        {/* Left Column */}
        <div className='col-span-6 space-y-1'>
          <div className='flex items-center'>
            <Label className='w-36 font-semibold'>Loại hóa đơn</Label>

            <FormField
              name='ma_ngv'
              type='select'
              disabled={formMode === 'view'}
              options={[
                { value: '1', label: '1. Giảm giá' },
                { value: '2', label: '2. Tăng giá' },
                { value: '3', label: '3. Giảm giá GTGT' },
                { value: '4', label: '4. Tăng giá GTGT' },
                { value: '5', label: '5. Giảm số lượng' }
              ]}
              className='w-[205px]'
            />
          </div>
          <div className='flex items-center'>
            <Label className='w-36 font-semibold'>Mã khách hàng</Label>
            <SearchField<KhachHang>
              type='text'
              value={state.khachHang?.customer_code || ''}
              searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
              searchColumns={khachHangSearchColumns}
              onRowSelection={actions.setKhachHang}
              columnDisplay='customer_code'
              dialogTitle='Danh mục khách hàng'
              className='w-[205px]'
              disabled={formMode === 'view'}
            />

            <div className='ml-4 flex items-center'>
              <Label className='w-20 font-semibold'>Mã số thuế</Label>
              <FormField
                name='ma_so_thue'
                type='text'
                disabled={formMode === 'view'}
                placeholder='Nhập và tra cứu'
                value={state.khachHang?.tax_code || ''}
              />
              <button className='ml-2 flex items-center justify-center border border-gray-200 px-3 py-1.5'>
                <AritoIcon icon={15} className='shrink-0' />
              </button>
              <button className='ml-2 flex items-center justify-center border border-gray-200 px-3 py-1.5'>
                <AritoIcon icon={888} />
              </button>
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-36 font-semibold'>Tên khách hàng</Label>
            <div className='flex-1'>
              <FormField
                name='ten_kh_thue'
                type='text'
                disabled={formMode === 'view'}
                value={state.khachHang?.customer_name || ''}
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-36 font-semibold'>Địa chỉ</Label>
            <div className='flex-1'>
              <FormField
                name='dia_chi'
                type='text'
                disabled={formMode === 'view'}
                labelClassName='w-48'
                value={state.khachHang?.address || ''}
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-36 font-semibold'>Tài khoản</Label>
            <SearchField<AccountModel>
              type='text'
              value={state.khachHang?.account_data?.code || state.taiKhoan?.code || ''}
              relatedFieldValue={state.khachHang?.account_data?.name || state.taiKhoan?.name || ''}
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              columnDisplay='code'
              displayRelatedField='name'
              searchColumns={accountSearchColumns}
              dialogTitle='Danh sách tài khoản'
              onRowSelection={actions.setTaiKhoan}
              disabled={formMode === 'view'}
            />
          </div>

          <div className='flex items-center'>
            <div className='flex-1'>
              <FormField
                label='Diễn giải'
                name='dien_giai'
                type='text'
                disabled={formMode === 'view'}
                labelClassName='font-semibold w-36'
              />
            </div>
          </div>
        </div>

        {/* Middle Column */}
        <div className='col-span-3 space-y-1'>
          <div className='ml-4 flex h-8 items-center'></div>

          <div className='ml-4 flex items-center'>
            <FormField
              name='ong_ba'
              label='Người nhận'
              labelClassName='font-semibold w-32'
              inputClassName='w-6 text-red-600'
              disabled={formMode === 'view'}
              className='w-[350px]'
              value={state.khachHang?.contact_person || ''}
              placeholder='Nhập tên người nhận'
            />
          </div>

          <div className='ml-4 flex items-center'>
            <FormField
              name='e_mail'
              label='Email'
              labelClassName='font-semibold w-32'
              inputClassName='w-64'
              className='w-[350px]'
              disabled={formMode === 'view'}
              value={state.khachHang?.email || ''}
              placeholder='Nhập email'
            />
          </div>
          <div className='ml-4 flex items-center'>
            <Label className='w-32 font-semibold'>Hạn thanh toán</Label>
            <SearchField<HanThanhToan>
              type='text'
              value={state.hanThanhToan?.ma_tt || ''}
              searchEndpoint={`/${QUERY_KEYS.HAN_THANH_TOAN}/`}
              columnDisplay='ma_tt'
              searchColumns={hanThanhToanSearchColumns}
              dialogTitle='Danh mục hạn thanh toán'
              onRowSelection={actions.setHanThanhToan}
              placeholder='Hạn thanh toán'
              relatedFieldValue={state.hanThanhToan?.ten_tt || ''}
              displayRelatedField='ten_tt'
              className='w-[205px]'
              disabled={formMode === 'view'}
            />
          </div>
        </div>

        {/* Right Column */}
        <div className='col-span-3 flex'>
          {/* Tab Content */}
          <div className='flex-1 pr-2'>
            <DocumentNumberField
              ma_ct={MA_CHUNG_TU.BAN_HANG.HOA_DON_DIEU_CHINH_GIA_HANG_BAN}
              quyenChungTu={state.quyenChungTu}
              onQuyenChungTuChange={actions.setQuyenChungTu}
              soChungTu={state.soChungTu}
              onSoChungTuChange={actions.setSoChungTu}
              disabled={formMode === 'view'}
              classNameSearchField='w-full'
              labelClassName='font-semibold font-semibold w-32'
              formMode={formMode}
            />

            <div className='flex items-center'>
              <Label className='w-32 font-semibold'>Ngày chứng từ</Label>
              <div className='flex-1'>
                <FormField name='ngay_ct' type='date' disabled={formMode === 'view'} />
              </div>
            </div>

            <CurrencyInput formMode={formMode} classNameInput='w-full' labelClassName='font-semibold w-32' />

            <div className='flex items-center'>
              <Label className='w-32 font-semibold'>Trạng thái</Label>
              <div className='flex-1'>
                <FormField
                  name='status'
                  type='select'
                  disabled={formMode === 'view'}
                  options={[
                    { value: '0', label: 'Chưa ghi sổ' },
                    { value: '3', label: 'Chờ duyệt' },
                    { value: '5', label: 'Đã ghi sổ' },
                    { value: '9', label: 'Huỷ' }
                  ]}
                />
              </div>
            </div>

            <div className='mt-4 flex'>
              <div className='mb-4 h-2 w-32 shrink-0' />
              <FormField
                label='Dữ liệu được nhận'
                name='transfer_yn'
                type='checkbox'
                disabled={true}
                labelClassName='font-semibold w-32'
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
