'use client';

import React from 'react';
import { EditPrintTemplateDialog } from './components/edit-print-template';
import { useDialogState, useTableData, useActionHandlers } from './hooks';
import AritoDataTables from '@/components/custom/arito/data-tables';
import InitialSearchDialog from './components/InitialSearchDialog';
import ActionBar from './components/ActionBar';

export default function SalesOrderStatusReportPage() {
  const {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,
    searchParams,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate

    // tables,
    // handleRowClick,
    // isLoading,
    // error,
    // refreshData,
    // postData,

    // handleRefreshClick,
    // handleFixedColumnsClick,
    // handleExportDataClick
  } = useDialogState();
  const { tables, handleRowClick, isLoading, error, refreshData, totalItems, currentPage, handlePageChange } =
    useTableData(searchParams);

  const { handleRefreshClick, handleFixedColumnsClick, handleExportDataClick } = useActionHandlers(refreshData);

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleInitialSearch}
      />

      <EditPrintTemplateDialog
        open={editPrintTemplateDialogOpen}
        onClose={handleClosePrintTemplateDialog}
        onSave={handleSavePrintTemplate}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportDataClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
            className='border-b border-gray-200'
            searchParams={searchParams}
          />

          <div className='flex-1 overflow-hidden'>
            {isLoading ? (
              <div className='flex h-full items-center justify-center'>
                <div className='text-gray-500'>Đang tải dữ liệu...</div>
              </div>
            ) : error ? (
              <div className='flex h-full items-center justify-center'>
                <div className='text-red-500'>Lỗi: {error.message}</div>
              </div>
            ) : (
              <AritoDataTables
                tables={tables}
                onRowClick={handleRowClick}
                pageSize={20}
                totalItems={totalItems}
                currentPage={currentPage}
                onPageChange={handlePageChange}
                serverSidePagination={true}
              />
            )}
          </div>
        </>
      )}
    </div>
  );
}
