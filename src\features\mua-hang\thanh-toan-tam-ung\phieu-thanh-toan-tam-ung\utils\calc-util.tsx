export interface CalculationResult {
  totalAmount: number;
  totalTax: number;
  totalPayment: number;
}

function safeToNumber(value: any): number {
  if (value === null || value === undefined || value === '') {
    return 0;
  }

  const num = Number(value);
  return isNaN(num) ? 0 : num;
}

export function calculateTotals(detailItems: any[] = [], taxDetails: any[] = []): CalculationResult {
  const totalAmount = detailItems.reduce((sum, item, index) => {
    const itemAmount = safeToNumber(item?.tien_nt);
    return sum + itemAmount;
  }, 0);

  const totalTax = taxDetails.reduce((sum, tax, index) => {
    const taxAmount = safeToNumber(tax?.t_thue_nt);
    return sum + taxAmount;
  }, 0);

  const totalPayment = totalAmount + totalTax;

  return {
    totalAmount,
    totalTax,
    totalPayment
  };
}
