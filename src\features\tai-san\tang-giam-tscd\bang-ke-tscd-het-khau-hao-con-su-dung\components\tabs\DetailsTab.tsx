import { useFormContext } from 'react-hook-form';
import React from 'react';
import { QUERY_KEYS, nhomColumns, BoPhanSuDungCCDCSearchColumns, loaiTaiSanSearchColumns } from '@/constants';
import { SearchField, FormField } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';

const DetailsTab: React.FC = () => {
  const { setValue } = useFormContext();

  return (
    <div className='w-[800px] min-w-[800px] space-y-4 p-4'>
      <div className='flex flex-col space-y-3'>
        {/* 1. Loại tài sản */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Loại tài sản:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.LOAI_TAI_SAN_CONG_CU}/`}
              searchColumns={loaiTaiSanSearchColumns}
              dialogTitle='Danh mục loại tài sản'
              columnDisplay='ma_lts'
              displayRelatedField='ten_lts'
              onRowSelection={row => {
                if (row) {
                  setValue('ma_lts_data', row);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* 2. Bộ phận sử dụng */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Bộ phận sử dụng:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.BO_PHAN_SU_DUNG_TSCD}/`}
              searchColumns={BoPhanSuDungCCDCSearchColumns}
              dialogTitle='Danh mục bộ phận'
              columnDisplay='ma_bp'
              displayRelatedField='ten_bp'
              onRowSelection={row => {
                if (row) {
                  setValue('ma_bp_data', row);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* 3. Nhóm tài sản */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Nhóm tài sản 1,2,3</Label>
          <div className='flex gap-2'>
            <SearchField<any>
              searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=TS1`}
              searchColumns={nhomColumns}
              dialogTitle='Danh mục nhóm tài sản 1'
              columnDisplay='ma_nhom'
              onRowSelection={row => {
                if (row) {
                  setValue('nh_ts1_data', row);
                }
              }}
            />
            <SearchField<any>
              searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=TS2`}
              searchColumns={nhomColumns}
              dialogTitle='Danh mục nhóm tài sản 2'
              columnDisplay='ma_nhom'
              onRowSelection={row => {
                if (row) {
                  setValue('nh_ts2_data', row);
                }
              }}
            />
            <SearchField<any>
              searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=TS3`}
              searchColumns={nhomColumns}
              dialogTitle='Danh mục nhóm tài sản 3'
              columnDisplay='ma_nhom'
              onRowSelection={row => {
                if (row) {
                  setValue('nh_ts3_data', row);
                }
              }}
            />
          </div>
        </div>

        {/* 4. Mẫu báo cáo */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mẫu báo cáo:</Label>
          <div className='w-64'>
            <FormField
              name='mau_bc'
              type='select'
              options={[
                { value: '20', label: 'Mẫu tiền chuẩn' },
                { value: '21', label: 'Mẫu ngoại tệ' }
              ]}
              className='w-full'
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
