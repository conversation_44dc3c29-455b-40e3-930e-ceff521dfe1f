import { <PERSON>Field, FormField, CurrencyInput } from '@/components/custom/arito';
import { MA_CHUNG_TU, QUERY_KEYS, accountSearchColumns } from '@/constants';
import { DocumentNumberField } from '@/components/custom/arito';
import { FormFieldState, FormFieldActions } from '../../hooks';
import { AccountModel } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
  formState: {
    state: FormFieldState;
    actions: FormFieldActions;
  };
}

export function BasicInfoTab({ formMode, formState: { state, actions } }: BasicInfoTabProps) {
  return (
    <div className='space-y-2 p-3'>
      <div className='space-y-1'>
        <div className='grid grid-cols-5 gap-4'>
          <div className='col-span-4 flex flex-col items-baseline gap-2'>
            <div className='flex items-center'>
              <Label className='w-32 min-w-32'><PERSON><PERSON><PERSON> phiếu chi</Label>
              <div className='w-[250px]'>
                <FormField
                  name='ma_ngv'
                  type='select'
                  options={[
                    { value: '1', label: '1. Chi theo hóa đơn' },
                    { value: '2', label: '2. Chi theo đối tượng' },
                    { value: '3', label: '3. Chi khác' },
                    { value: '4', label: '4. Gửi tiền vào ngân hàng' }
                  ]}
                  value={state.loaiPhieuChi}
                  onValueChange={actions.setLoaiPhieuChi}
                  disabled={formMode === 'view'}
                />
              </div>
            </div>

            <div className='flex w-full items-center'>
              <Label className='w-32 min-w-32'>Địa chỉ</Label>
              <div className='flex-1'>
                <FormField name='dia_chi' type='text' disabled={formMode === 'view'} />
              </div>
            </div>

            <div className='flex w-full items-center'>
              <Label className='w-32 min-w-32'>Người nhận tiền</Label>
              <div className='flex-1'>
                <FormField name='ong_ba' type='text' disabled={formMode === 'view'} className='w-full' />
              </div>
            </div>

            <div className='flex w-full items-center'>
              <Label className='w-32 min-w-32'>Diễn giải</Label>
              <div className='flex-1'>
                <FormField name='dien_giai' type='text' disabled={formMode === 'view'} />
              </div>
            </div>

            <div className='flex items-center'>
              <Label className='w-32 min-w-32'>Tài khoản có</Label>
              <SearchField<AccountModel>
                name='tk'
                type='text'
                disabled={formMode === 'view'}
                columnDisplay='code'
                displayRelatedField='name'
                searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
                searchColumns={accountSearchColumns}
                value={state.taiKhoan?.code || ''}
                relatedFieldValue={state.taiKhoan?.name || ''}
                onRowSelection={actions.setTaiKhoan}
                dialogTitle='Danh mục tài khoản'
              />
            </div>
          </div>

          <div className='col-start-5 flex flex-col items-start gap-2'>
            <DocumentNumberField
              ma_ct={MA_CHUNG_TU.TIEN_MAT.PHIEU_CHI}
              quyenChungTu={state.quyenChungTu}
              onQuyenChungTuChange={actions.setQuyenChungTu}
              soChungTu={state.soChungTu || ''}
              onSoChungTuChange={actions.setSoChungTu}
              disabled={formMode === 'view'}
              classNameSearchField='w-full'
            />

            <div className='flex items-center'>
              <Label className='w-32 min-w-32'>Ngày chứng từ</Label>
              <FormField name='ngay_ct' type='date' disabled={formMode === 'view'} />
            </div>

            <div className='flex items-center'>
              <Label className='w-32 min-w-32'>Ngày lập chứng từ</Label>
              <FormField name='ngay_lct' type='date' disabled={formMode === 'view'} />
            </div>

            <CurrencyInput formMode={formMode} classNameInput='w-full' />

            <div className='flex w-full items-center'>
              <Label className='w-32 min-w-32'>Trạng thái</Label>
              <div className='flex-1'>
                <FormField
                  name='status'
                  type='select'
                  disabled={formMode === 'view'}
                  options={[
                    { value: '0', label: 'Chưa ghi sổ' },
                    { value: '3', label: 'Chờ duyệt' },
                    { value: '5', label: 'Đã ghi sổ' },
                    { value: '6', label: 'Đang thực hiện' },
                    { value: '7', label: 'Hoàn thành' }
                  ]}
                />
              </div>
            </div>

            <div className='flex items-center'>
              <Label className='w-32 min-w-32'></Label>
              <FormField name='transfer_yn' type='checkbox' disabled={true} label='Dữ liệu được nhận' />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
