import { transformDocumentNumber } from '@/components/custom/arito/form/document-number/util';
import { isValidUUID } from '@/lib/uuid-validator';
import type { FormMode } from '@/types/form';
import { MA_CHUNG_TU } from '@/constants';
import { FormFieldState } from '../hooks';

/**
 * Transform detail rows for API submission
 * @param detailRows - Array of detail row data from the form
 * @returns Transformed detail rows ready for API submission
 */
export const transformDetailRows = (detailRows: any[], formMode: FormMode) => {
  return detailRows?.map((row: any, index: number) => {
    const baseData = {
      line: index + 1,
      ma_dv: row.ma_dv_data?.uuid || null,
      tk_vt: row.tk_vt_data?.uuid || row.ma_dv_data?.tk_cp_data?.uuid || null,
      dien_giai: row.dien_giai || row.ma_dv_data?.ten_dv || null,
      ma_lts: row.ma_lts_data?.uuid || null,
      dvt: row.ma_dv_data?.dvt || null,
      so_luong: row.so_luong || 0,
      gia_nt: row.gia_nt || row.ma_dv_data?.gia_nt || 0.0,
      gia: row.gia || 0.0,
      tien_nt: row.tien_nt || 0.0,
      tien: row.tien || 0.0,
      tien_tck_nt: row.tien_tck_nt || 0.0,
      tl_ck: row.tl_ck || 0.0,
      ck_nt: row.ck_nt || 0.0,
      tien_tck: row.tien_tck || 0.0,
      ck: row.ck || 0.0,
      ma_thue: row.ma_thue_data?.uuid || row.ma_dv_data?.ma_thue || null,
      thue_nt: row.thue_nt || 0.0,
      thue_suat: row.ma_thue_data?.thue_suat || row.ma_dv_data?.thue_suat_data?.thue_suat || 0.0,
      thue: row.thue_nt || 0.0,
      ma_bp: row.ma_bp_data?.uuid || row.ma_dv_data?.ma_bp_data?.uuid || null,
      ma_vv: row.ma_vv_data?.uuid || row.ma_dv_data?.ma_vv_data?.uuid || null,
      ma_hd: row.ma_hd_data?.uuid || row.ma_dv_data?.ma_hd_data?.uuid || null,
      ma_dtt: row.ma_dtt_data?.uuid || row.ma_dv_data?.ma_dtt_data?.uuid || null,
      ma_ku: row.ma_ku_data?.uuid || row.ma_dv_data?.ma_ku_data?.uuid || null,
      ma_phi: row.ma_phi_data?.uuid || row.ma_dv_data?.ma_phi_data?.uuid || null,
      ma_sp: row.ma_sp_data?.uuid || null,
      ma_lsx: row.ma_lsx_data?.uuid || null,
      ma_cp0: row.ma_cp0_data?.uuid || null
    };

    // Chỉ thêm uuid nếu formMode !== 'add' và uuid hợp lệ
    if (formMode !== 'add' && isValidUUID(row.uuid)) {
      return { uuid: row.uuid, ...baseData };
    }

    return baseData;
  });
};

/**
 * Transform tax rows for API submission
 * @param taxRows - Array of tax row data from the form
 * @returns Transformed tax rows ready for API submission
 */
export const transformTaxData = (taxRows: any[], formMode: FormMode) => {
  return taxRows?.map((row: any, index: number) => {
    const baseData = {
      line: index + 1,
      so_ct0: row.so_ct0 || null,
      so_ct2: row.so_ct2 || null,
      ngay_ct0: row.ngay_ct0 || null,
      ma_mau_bc: row.ma_mau_bc || null,
      ma_tc_thue: row.ma_tc_thue_data || null,
      ma_kh: row.ma_kh_data?.uuid || null,
      ten_kh_thue: row.ma_kh_data?.customer_name || null,
      dia_chi: row.ma_kh_data?.address || null,
      ma_so_thue: row.ma_kh_data?.tax_code || null,
      ten_vt_thue: row.ten_vt_thue || null,
      t_tien_nt: row.t_tien_nt || 0.0,
      t_tien: row.t_tien || 0.0,
      t_thue_nt: row.t_thue_nt || 0.0,
      t_thue: row.t_thue || 0.0,
      tk_thue_no: row.tk_thue_data?.uuid || null,
      ma_thue: row.ma_thue_data?.uuid,
      thue_suat: row.ma_thue_data?.thue_suat || 0.0,
      ma_kh9: row.ma_kh9_data?.uuid || null,
      ghi_chu: row.ghi_chu || null,
      ma_bp: row.ma_bp_data?.uuid || null,
      ma_vv: row.ma_vv_data?.uuid || null,
      ma_hd: row.ma_hd_data?.uuid || null,
      ma_dtt: row.ma_dtt_data?.uuid || null,
      ma_ku: row.ma_ku_data?.uuid || null,
      ma_phi: row.ma_phi_data?.uuid || null,
      ma_sp: row.ma_sp_data?.uuid || null,
      ma_lsx: row.ma_lsx_data?.uuid || null,
      ma_cp0: row.ma_cp0_data?.uuid || null
    };

    // Chỉ thêm uuid nếu formMode !== 'add' và uuid hợp lệ
    if (formMode !== 'add' && isValidUUID(row.uuid)) {
      return { uuid: row.uuid, ...baseData };
    }

    return baseData;
  });
};

/**
 * Transform TSCC rows for API submission
 * @param tscRows - Array of TSCC row data from the form
 * @returns Transformed TSCC rows ready for API submission
 */
export const transformTSCCData = (tscRows: any[], formMode: FormMode) => {
  return tscRows?.map((row: any, index: number) => {
    const baseData = {
      line: index + 1,

      // Thông tin tài sản cơ bản
      ma_ts: row.ma_ts || null,
      ten_ts: row.ten_ts || null,
      ma_lts: row.ma_lts_data?.ma_lts || null,

      // Thông tin thời gian
      ngay_mua: row.ngay_mua || null,
      ngay_kh0: row.ngay_kh0 || null,
      ngay_kh_kt: row.ngay_kh_kt || null,
      so_ky_kh: row.so_ky_kh || null,

      // Thông tin giá trị tài chính
      nguyen_gia_nt: row.nguyen_gia_nt || 0,
      gt_da_kh_nt: row.gt_da_kh_nt || 0,
      gt_cl_nt: row.gt_cl_nt || 0,
      gt_kh_ky_nt: row.gt_kh_ky_nt || 0,

      // Thông tin khác
      so_hieu_ts: row.so_hieu_ts || null,
      dvt: row.dvt || null,
      so_luong: row.so_luong || 0,

      // Thông tin tài khoản
      tk_ts: row.tk_ts_data?.uuid || null,
      tk_kh: row.tk_kh_data?.uuid || null,
      tk_cp: row.tk_cp_data?.uuid || null,

      // Thông tin phân bổ
      ma_bp: row.ma_bp_data?.uuid || null,
      ma_vv: row.ma_vv_data?.uuid || null,
      ma_phi: row.ma_phi_data?.uuid || null,

      // Thông tin data objects (để giữ reference)
      ma_lts_data: row.ma_lts_data?.uuid || null,
      tk_ts_data: row.tk_ts_data?.uuid || null,
      tk_kh_data: row.tk_kh_data?.uuid || null,
      tk_cp_data: row.tk_cp_data?.uuid || null,
      ma_bp_data: row.ma_bp_data?.uuid || null,
      ma_vv_data: row.ma_vv_data?.uuid || null,
      ma_phi_data: row.ma_phi_data?.uuid || null
    };

    // Chỉ thêm uuid nếu formMode !== 'add' và uuid hợp lệ
    if (formMode !== 'add' && isValidUUID(row.uuid)) {
      return { uuid: row.uuid, ...baseData };
    }

    return baseData;
  });
};

/**
 * Transform all form data for submission
 * @param data - Form data from the form submission
 * @param state - Form field state containing references to selected entities
 * @param detailRows - Array of detail row data
 * @param taxRows - Array of tax row data
 * @param tscRows - Array of TSCC row data
 * @returns Transformed data ready for API submission
 */
export const transformFormData = (
  data: any,
  state: FormFieldState,
  formMode: FormMode,
  tong_thue: number,
  tong_tien: number,
  tong_chiet_khau: number,
  tong_thanh_toan: number,
  ...rest: any[]
) => {
  // Transform detail and account rows
  const detail = transformDetailRows(rest[0], formMode);
  const tax = transformTaxData(rest[1], formMode);
  const tai_san = state.ma_gd == 'TS' ? transformTSCCData(rest[2], formMode) : [];

  return {
    ...data,
    // Form state checkboxes
    ma_gd: state.ma_gd,
    // px_yn: state.px_yn,
    pc_tao_yn: state.pc_tao_yn,
    ck_yn: state.ck_yn,
    ck_tl_nt: state.chietKhau || 0,
    loai_ck: state.loai_ck,

    // Payment method from state
    ma_httt: state.ma_httt || 'TMB',

    // Customer information
    ma_kh: state.nhaCungCap?.uuid || null,
    ma_so_thue: data.ma_so_thue || state.nhaCungCap?.tax_code || null,
    ten_kh_thue: data.ten_kh_thue || state.nhaCungCap?.customer_name || null,
    dia_chi: data.dia_chi || state.nhaCungCap?.address || null,
    ong_ba: data.ong_ba || state.nhaCungCap?.contact_person || null,
    e_mail: data.e_mail || state.nhaCungCap?.email || null,
    dien_giai: data.dien_giai || state.nhaCungCap?.description || null,
    so_ct0: data.so_ct0 || null,

    // References
    ma_nvmh: state.nhanVien?.uuid || state.nhaCungCap?.sales_rep_data?.uuid || null,
    tk: state.taiKhoan?.uuid || state.nhaCungCap?.account_data?.uuid || null,
    ma_tt: state.hanThanhToan?.uuid || state.nhaCungCap?.payment_term_data?.uuid || null,

    // Document information
    ma_ngv: '1',
    ...transformDocumentNumber(state.quyenChungTu, state.soChungTu, MA_CHUNG_TU.MUA_HANG.HOA_DON_MUA_DICH_VU),
    so_ct2: data.so_ct2 || null,
    ngay_ct: data.ngay_ct || null,
    ngay_lct: data.ngay_ct || null,

    // Totals
    t_tien_nt: tong_tien,
    t_tien: tong_tien,
    t_thue_nt: tong_thue,
    t_thue: tong_thue,
    t_tt_nt: tong_thanh_toan,
    t_tt: tong_thanh_toan,
    t_ck_nt: tong_chiet_khau,
    t_ck: tong_chiet_khau,

    // Detail arrays
    chi_tiet: detail,
    thue: tax,
    tai_san: tai_san
  };
};
