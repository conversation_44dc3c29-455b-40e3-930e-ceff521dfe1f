import { Button } from '@mui/material';
import React from 'react';
import BasicPrintInfoTab from '@/components/cac-loai-form/print-form/BasicPrintInfoTab';
import { AritoIcon, AritoForm, AritoHeaderTabs } from '@/components/custom/arito';
import PrintColTab from '@/components/cac-loai-form/print-form/PrintColTab';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { searchSchema, initialValues } from '../../schema';

interface EditPrintTemplateDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (values: any) => void;
}

export const EditPrintTemplateDialog: React.FC<EditPrintTemplateDialogProps> = ({ open, onClose, onSave }) => {
  const handleFormSubmit = async (data: any) => {
    try {
      onSave(data);
      onClose();
    } catch (error) {
      console.error('Error saving print template:', error);
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Chỉnh sửa mẫu in'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={699} />}
    >
      <AritoForm
        mode='edit'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={handleFormSubmit}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
            <BasicPrintInfoTab formMode='edit' />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'print-columns',
                  label: 'Cột in',
                  component: <PrintColTab formMode='edit' value={[]} onChange={() => {}} />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 px-2'
        bottomBar={
          <>
            <Button
              className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
              type='submit'
              variant='contained'
            >
              <AritoIcon icon={884} marginX='4px' />
              Lưu
            </Button>

            <Button onClick={onClose} variant='outlined'>
              <AritoIcon icon={885} marginX='4px' />
              Huỷ
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
};

export default EditPrintTemplateDialog;
