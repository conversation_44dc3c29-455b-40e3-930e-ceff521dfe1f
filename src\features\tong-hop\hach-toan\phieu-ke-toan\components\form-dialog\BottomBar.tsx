import React from 'react';
import { formatMoney } from '@/lib/formatUtils';

export interface BottomBarProps {
  t_ps_no_nt?: number;
  t_ps_co_nt?: number;
  formMode: 'add' | 'edit' | 'view';
}

export const BottomBar: React.FC<BottomBarProps> = ({ t_ps_no_nt = 0, t_ps_co_nt = 0, formMode }) => {
  return (
    <div className='bottom-0 w-full border-t bg-white px-4 py-4 pb-2 shadow-md lg:fixed lg:flex lg:items-center lg:justify-between lg:px-0 lg:pb-1'>
      {/* Left side - Total foreign currency debit & credit */}
      <div className='flex w-full flex-col gap-6 lg:w-1/2 lg:pl-5'>
        <div className='grid w-full grid-cols-[160px,1fr]'>
          <div className='text-sm font-bold'>Tổng ps nợ NT</div>
          <div className='flex items-center justify-end border-b-[1px] border-gray-200 text-right text-sm font-bold lg:text-left'>
            {formatMoney(t_ps_no_nt)}
          </div>
        </div>
        <div className='grid w-full grid-cols-[160px,1fr]'>
          <div className='text-sm font-bold'>Tổng ps có NT</div>
          <div className='flex items-center justify-end border-b-[1px] border-gray-200 text-right text-sm font-bold lg:text-left'>
            {formatMoney(t_ps_co_nt)}
          </div>
        </div>
      </div>
    </div>
  );
};
