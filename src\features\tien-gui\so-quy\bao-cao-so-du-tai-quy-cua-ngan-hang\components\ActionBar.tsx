import { Plus, RefreshCw, Table, FileDown, Printer } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import { AritoIcon } from '@/components/custom/arito';

interface Props {
  onSearchClick: () => void;
  onRefreshClick: () => void;
  onFixedColumnsClick: () => void;
  onExportDataClick: () => void;
  onEditPrintTemplateClick: () => void;
  className?: string;
  searchParams?: any;
}

export function ActionBar({
  className,
  onSearchClick,
  onRefreshClick,
  onFixedColumnsClick,
  onExportDataClick,
  onEditPrintTemplateClick,
  searchParams
}: Props) {
  return (
    <AritoActionBar
      titleComponent={<h1 className='relative text-xl font-bold'>Báo cáo số dư tại quỹ của ngân hàng</h1>}
      className={className}
    >
      <>
        <AritoActionButton title='Tìm kiếm' icon={Plus} onClick={onSearchClick} variant='primary' shortcut='Alt + N' />
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} />
        <AritoActionButton title='Cố định cột' icon={Table} onClick={onFixedColumnsClick} />
        <AritoActionButton
          title='Kết xuất dữ liệu'
          icon={FileDown}
          onClick={onExportDataClick}
          shortcut='Alt + D, Ctrl + D'
        />
        <AritoMenuButton
          items={[
            {
              title: 'Chỉnh sửa mẫu in',
              icon: <AritoIcon icon={555} />,
              onClick: onEditPrintTemplateClick,
              group: 0
            }
          ]}
        />
      </>
    </AritoActionBar>
  );
}

export default ActionBar;
