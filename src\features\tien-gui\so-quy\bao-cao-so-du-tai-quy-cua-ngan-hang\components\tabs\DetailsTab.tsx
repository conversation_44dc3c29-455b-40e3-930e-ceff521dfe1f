import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { reportTemplateOptions } from '../../schema';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const DetailTab = ({ formMode }: Props) => {
  return (
    <div className='grid min-h-[100px] grid-cols-1 gap-x-8 space-y-2 p-4 lg:grid-cols-1 lg:space-y-0'>
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Mẫu báo cáo'
        name='mau_bc'
        type='select'
        disabled={formMode === 'view'}
        options={reportTemplateOptions}
      />
    </div>
  );
};

export default DetailTab;
