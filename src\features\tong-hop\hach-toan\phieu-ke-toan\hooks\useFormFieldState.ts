import { useState } from 'react';
import { Account<PERSON><PERSON><PERSON>, KhachHang, NhanVien, Ngoai<PERSON><PERSON>, DonViCoSo, type QuyenChungTu } from '@/types/schemas';

export interface FormFieldState {
  quyenChungTu: QuyenChungTu | null;
  soChungTu: string | null;
}

export interface FormFieldActions {
  setQuyenChungTu: (quyenChungTu: QuyenChungTu) => void;
  setSoChungTu: (soChungTu: string) => void;

  resetState: () => void;
  updateState: (updates: Partial<FormFieldState>) => void;
}

export interface UseFormFieldStateReturn {
  state: FormFieldState;
  actions: FormFieldActions;
}

const initialState: FormFieldState = {
  quyenChungTu: null,
  soChungTu: null
};

function transformInitialData(initialData?: any): Partial<FormFieldState> {
  if (!initialData) return {};

  return {
    quyenChungTu: initialData.ma_nk_data || null,
    soChungTu: initialData.so_ct || null
  };
}

export function useFormFieldState(initialData?: any): UseFormFieldStateReturn {
  const [state, setState] = useState<FormFieldState>({
    ...initialState,
    ...transformInitialData(initialData)
  });

  const actions: FormFieldActions = {
    setQuyenChungTu: (quyenChungTu: QuyenChungTu) => {
      setState(prev => ({
        ...prev,
        quyenChungTu
      }));
    },

    setSoChungTu: (soChungTu: string) => {
      setState(prev => ({
        ...prev,
        soChungTu
      }));
    },

    resetState: () => {
      setState(initialState);
    },

    updateState: (updates: Partial<FormFieldState>) => {
      setState(prev => ({ ...prev, ...updates }));
    }
  };

  return { state, actions };
}
