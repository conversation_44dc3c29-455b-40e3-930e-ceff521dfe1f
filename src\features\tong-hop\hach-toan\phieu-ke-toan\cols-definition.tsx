import { GridColDef, GridRenderCellParams, GridCellParams } from '@mui/x-data-grid';

export const getTableColumns = (handleOpenViewForm: (obj: any) => void): GridColDef[] => [
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 100,
    renderCell: (params: GridRenderCellParams) => {
      const statusMap: { [key: number]: string } = {
        0: 'Chưa ghi sổ',
        3: 'Chờ duyệt',
        5: 'Đã ghi sổ'
      };
      return statusMap[params.row.status] || 'Không xác định';
    }
  },
  {
    field: 'so_ct',
    headerName: 'Số chứng từ',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <div className='cursor-pointer text-teal-600 hover:underline' onClick={() => handleOpenViewForm(params.row)}>
        {params.value}
      </div>
    )
  },
  { field: 'ngay_ct', headerName: 'Ngày c/từ', width: 120 },
  { field: 'dien_giai', headerName: '<PERSON><PERSON><PERSON> gi<PERSON>i', width: 250 },
  { field: 't_ps_no_nt', headerName: 'Tổng phát sinh', type: 'number', width: 150 },
  {
    field: 'ma_nt',
    headerName: 'Ngoại tệ',
    width: 120,
    renderCell: params => <div className='text-center'>{params.row.ma_nt_data?.ma_nt} </div>
  },
  { field: 'username0', headerName: 'Người tạo', width: 150 },
  { field: 'datetime0', headerName: 'Ngày tạo', width: 150 },
  { field: 'username2', headerName: 'Người sửa', width: 150 },
  { field: 'datetime2', headerName: 'Ngày sửa', width: 150 }
];

export const getInputTableColumns = (data?: any[]): GridColDef[] => {
  const hasDataForField = (fieldPath: string): boolean => {
    if (!data || data.length === 0) return false;

    return data.some(row => {
      const fieldParts = fieldPath.split('.');
      let value = row;

      for (const part of fieldParts) {
        value = value?.[part];
        if (value === undefined || value === null) return false;
      }

      return value !== '' && value !== 0;
    });
  };

  const columns = [
    {
      field: 'tk',
      headerName: 'Tài khoản',
      width: 120,
      renderCell: (params: GridCellParams) => params.row.tk_data?.code
    },
    {
      field: 'ten_tk',
      headerName: 'Tên tài khoản',
      width: 200,
      renderCell: (params: GridCellParams) => params.row.tk_data?.name
    },
    hasDataForField('ma_kh_data.customer_code') && {
      field: 'ma_kh',
      headerName: 'Mã đối tượng',
      width: 120,
      renderCell: (params: GridCellParams) => params.row.ma_kh_data?.customer_code
    },
    hasDataForField('ma_kh_data.customer_name') && {
      field: 'ten_kh',
      headerName: 'Tên đối tượng',
      width: 200,
      renderCell: (params: GridCellParams) => params.row.ma_kh_data?.customer_name
    },
    {
      field: 'ps_no_nt',
      headerName: 'Ps nợ VND',
      width: 120,
      type: 'number',
      renderCell: (params: GridCellParams) => params.row.ps_no_nt
    },
    {
      field: 'ps_co_nt',
      headerName: 'Ps có VND',
      width: 120,
      type: 'number',
      renderCell: (params: GridCellParams) => params.row.ps_co_nt
    },
    hasDataForField('nh_dk') && {
      field: 'nh_dk',
      headerName: 'Nhóm',
      width: 100,
      renderCell: (params: GridCellParams) => params.row.nh_dk
    },
    {
      field: 'dien_giai',
      headerName: 'Diễn giải',
      width: 250,
      renderCell: (params: GridCellParams) => params.row.dien_giai
    },
    hasDataForField('so_ct0') && {
      field: 'so_ct0',
      headerName: 'Số hóa đơn',
      width: 150,
      renderCell: (params: GridCellParams) => params.row.so_ct0
    },
    hasDataForField('ngay_ct0') && {
      field: 'ngay_ct0',
      headerName: 'Ngày hóa đơn',
      width: 150,
      renderCell: (params: GridCellParams) => params.row.ngay_ct0
    },

    hasDataForField('ma_bp_data.ma_bp') && {
      field: 'ma_bp',
      headerName: 'Bộ phận',
      width: 150,
      renderCell: (params: GridCellParams) => params.row.ma_bp_data?.ma_bp
    },

    hasDataForField('ma_vv_data.ma_vu_viec') && {
      field: 'ma_vv',
      headerName: 'Vụ việc',
      width: 150,
      renderCell: (params: GridCellParams) => params.row.ma_vv_data?.ma_vu_viec
    },

    hasDataForField('ma_hd_data.ma_hd') && {
      field: 'ma_hd',
      headerName: 'Hợp đồng',
      width: 150,
      renderCell: (params: GridCellParams) => params.row.ma_hd_data?.ma_hd
    },

    hasDataForField('ma_dtt_data.ma_dtt') && {
      field: 'ma_dtt',
      headerName: 'Đợt thanh toán',
      width: 150,
      renderCell: (params: GridCellParams) => params.row.ma_dtt_data?.ma_dtt
    },

    hasDataForField('ma_ku_data.ma_ku') && {
      field: 'ma_ku',
      headerName: 'Khế ước',
      width: 150,
      renderCell: (params: GridCellParams) => params.row.ma_ku_data?.ma_ku
    },

    hasDataForField('ma_phi_data.ma_phi') && {
      field: 'ma_phi',
      headerName: 'Phí',
      width: 120,
      renderCell: (params: GridCellParams) => params.row.ma_phi_data?.ma_phi
    },

    hasDataForField('ma_sp_data.ma_vt') && {
      field: 'ma_sp',
      headerName: 'Sản phẩm',
      width: 150,
      renderCell: (params: GridCellParams) => params.row.ma_sp_data?.ma_vt
    },

    hasDataForField('ma_lsx') && {
      field: 'ma_lsx',
      headerName: 'Lệnh sản xuất',
      width: 150,
      renderCell: (params: GridCellParams) => params.row.ma_lsx_data?.so_lsx
    },

    hasDataForField('ma_cp0_data.ma_cpkhl') && {
      field: 'ma_cp0',
      headerName: 'C/p không h/lệ',
      width: 150,
      renderCell: (params: GridCellParams) => params.row.ma_cp0_data?.ma_cpkhl
    }
  ];

  return columns.filter(Boolean) as GridColDef[];
};
