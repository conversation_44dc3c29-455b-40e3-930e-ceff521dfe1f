import { format } from 'date-fns';
import { z } from 'zod';

export const searchSchema = z.object({
  // BasicInfoTab fields
  ngay_ct1: z.string().optional().nullable(), // From date
  ngay_ct2: z.string().optional().nullable(), // To date
  so_ct1: z.string().optional().nullable(), // Document number start
  so_ct2: z.string().optional().nullable(), // Document number end
  ma_ngv: z.string().optional().nullable(), // Document type
  dien_giai: z.string().optional().nullable(), // Description

  // FilterTab fields
  ma_unit: z.string().optional().nullable(), // Unit code
  status: z.string().optional().nullable(), // Status
  user_id0: z.string().optional().nullable() // Filter by user
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const FormSchema = z.object({
  ma_ngv: z.string().optional().nullable(),
  dia_chi: z.string().optional().nullable(),
  ong_ba: z.string().optional().nullable(),
  dien_giai: z.string().optional().nullable(),
  so_ct: z.string().optional().nullable(),
  ngay_ct: z.string().optional().nullable(),
  ma_nt: z.string().optional().nullable(),
  ty_gia: z.string().optional().nullable(),
  status: z.string().optional().nullable(),
  transfer_yn: z.boolean().optional().nullable(),
  hd_yn: z.boolean().optional().nullable(),
  so_ct0: z.string().optional().nullable(),
  ngay_ct0: z.string().optional().nullable(),
  ngay_lct: z.string().optional().nullable(),
  ma_tt: z.string().optional().nullable(),
  tg_dd: z.boolean().optional().nullable(),
  cltg_yn: z.boolean().optional().nullable(),
  ma_kh: z.string().optional().nullable(),
  so_ct_goc: z.coerce.number().optional().nullable(),
  dien_giai_ct_goc: z.string().optional().nullable(),
  t_tien_nt: z.string().optional().nullable(),
  t_tien: z.string().optional().nullable()
});

export type FormSchemaValues = z.infer<typeof FormSchema>;

export const initialValues: FormSchemaValues = {
  ma_ngv: '1',
  dia_chi: '',
  ong_ba: '',
  dien_giai: '',
  ngay_ct: format(new Date(), 'yyyy-MM-dd'),
  ngay_lct: format(new Date(), 'yyyy-MM-dd'),
  ty_gia: '1',
  status: '0',
  transfer_yn: false,
  hd_yn: false,
  so_ct0: '',
  ngay_ct0: format(new Date(), 'yyyy-MM-dd'),
  ma_tt: '',
  tg_dd: false,
  cltg_yn: false,
  ma_kh: '',
  so_ct_goc: 0,
  dien_giai_ct_goc: '',
  t_tien_nt: '0',
  t_tien: '0'
};
